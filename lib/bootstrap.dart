import 'dart:async';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/firebase_options.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/widgets.dart';

class AppBlocObserver extends BlocObserver {
  const AppBlocObserver();

  @override
  void onChange(BlocBase<dynamic> bloc, Change<dynamic> change) {
    super.onChange(bloc, change);
    final logger = LoggerService();

    // Log BLoC state changes with structured format
    final fromState = change.currentState.runtimeType.toString();
    final toState = change.nextState.runtimeType.toString();
    logger.logBlocChange(bloc.runtimeType.toString(), fromState, toState);

    // Fallback to dart:developer log for development
    log(
      LoggingConstants.formatMessage(
        LoggingConstants.blocModule,
        LoggingConstants.blocStateChange,
        '${bloc.runtimeType}: $fromState → $toState',
      ),
    );
  }

  @override
  void onError(BlocBase<dynamic> bloc, Object error, StackTrace stackTrace) {
    // Log BLoC errors with structured format
    LoggerService()
        .logBlocError(bloc.runtimeType.toString(), error, stackTrace);

    // Fallback to dart:developer log for development
    log(
      LoggingConstants.formatError(
        LoggingConstants.blocModule,
        LoggingConstants.blocError,
        error.toString(),
        bloc.runtimeType.toString(),
      ),
      stackTrace: stackTrace,
    );

    super.onError(bloc, error, stackTrace);
  }
}

Future<void> bootstrap(FutureOr<Widget> Function() builder) async {
  // Ensure Flutter binding is initialized first
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logger service early
  final logger = LoggerService();

  // Enhanced error handling with logging
  FlutterError.onError = (details) {
    logger.error(
      LoggingConstants.formatError(
        LoggingConstants.appModule,
        LoggingConstants.criticalError,
        details.exceptionAsString(),
        'Flutter Framework Error',
      ),
      details.exception,
      details.stack,
    );

    // Fallback to dart:developer log
    log(details.exceptionAsString(), stackTrace: details.stack);
  };

  // Set up BLoC observer with logging
  Bloc.observer = const AppBlocObserver();

  // Log initialization steps
  logger
    ..info(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        LoggingConstants.appStarted,
        'Bootstrap process initiated',
      ),
    )
    ..debug(
      LoggingConstants.formatMessage(
        LoggingConstants.blocModule,
        'BLoC observer initialized',
      ),
    );

  try {
    // Initialize Firebase with logging
    logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.firebaseModule,
        'Initializing Firebase',
      ),
    );

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    logger
      ..info(
        LoggingConstants.formatMessage(
          LoggingConstants.firebaseModule,
          LoggingConstants.firebaseInitialized,
          'Platform: ${DefaultFirebaseOptions.currentPlatform.projectId}',
        ),
      )
      // Initialize dependency injection
      ..info(
        LoggingConstants.formatMessage(
          LoggingConstants.appModule,
          'Initializing dependency injection',
        ),
      );

    await Injection.initialize();

    logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Dependency injection initialized successfully',
      ),
    );
  } catch (error, stackTrace) {
    logger.error(
      LoggingConstants.formatError(
        LoggingConstants.firebaseModule,
        LoggingConstants.firebaseInitializationFailed,
        error.toString(),
      ),
      error,
      stackTrace,
    );
    rethrow;
  }

  // Add cross-flavor configuration here
  logger
    ..debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Cross-flavor configuration completed',
      ),
    )
    ..info(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Bootstrap completed successfully',
      ),
    );

  runApp(await builder());
}
