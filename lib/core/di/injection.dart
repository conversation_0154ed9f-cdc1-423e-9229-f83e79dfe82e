import 'package:bloomg_flutter/auth/cubit/auth_cubit.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/auth/repository/firebase_auth_repository.dart';
import 'package:bloomg_flutter/core/storage/hive_service.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:get_it/get_it.dart';

/// Global service locator instance.
final GetIt getIt = GetIt.instance;

/// {@template injection}
/// Dependency injection setup for the application.
/// {@endtemplate}
class Injection {
  /// Private constructor to prevent instantiation.
  Injection._();

  static final LoggerService _logger = LoggerService();

  /// Initializes all dependencies.
  static Future<void> initialize() async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Dependency injection initialization started',
      ),
    );

    try {
      // Initialize storage service first
      await _initializeStorage();

      // Register repositories
      _registerRepositories();

      // Register face verification dependencies
      _registerFaceVerificationDependencies();

      // Register cubits
      _registerCubits();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.appModule,
          'Dependency injection initialization completed',
        ),
      );
    } catch (e, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.appModule,
          LoggingConstants.criticalError,
          'Dependency injection initialization failed: $e',
          'Application may not function properly',
        ),
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Initializes storage services.
  static Future<void> _initializeStorage() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Initializing storage services',
      ),
    );

    // Initialize Hive service
    final hiveService = HiveService();
    await hiveService.initialize();

    // Register as singleton
    getIt.registerSingleton<HiveService>(hiveService);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Storage services initialized',
      ),
    );
  }

  /// Registers repository dependencies.
  static void _registerRepositories() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Registering repositories',
      ),
    );

    // Register AuthRepository
    getIt.registerLazySingleton<AuthRepository>(
      () => FirebaseAuthRepository(
        hiveService: getIt<HiveService>(),
      ),
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Repositories registered',
      ),
    );
  }

  /// Registers face verification dependencies.
  static void _registerFaceVerificationDependencies() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Registering face verification dependencies',
      ),
    );

    // Register face verification repositories
    getIt
      ..registerLazySingleton<FaceDetectionRepository>(
        FaceDetectionRepository.new,
      )
      ..registerLazySingleton<VideoStorageRepository>(
        VideoStorageRepository.new,
      )
      ..registerLazySingleton<VideoValidationService>(
        VideoValidationService.new,
      )
      ..registerLazySingleton<VideoGalleryRepository>(
        VideoGalleryRepository.new,
      )

      // Register face verification BLoC
      ..registerFactory<FaceVideoCaptureBloc>(
        () => FaceVideoCaptureBloc(
          faceDetectionRepository: getIt<FaceDetectionRepository>(),
          videoStorageRepository: getIt<VideoStorageRepository>(),
          videoValidationService: getIt<VideoValidationService>(),
        ),
      );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Face verification dependencies registered',
      ),
    );
  }

  /// Registers cubit dependencies.
  static void _registerCubits() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Registering cubits',
      ),
    );

    // Register AuthCubit as singleton to maintain global state
    getIt.registerLazySingleton<AuthCubit>(
      () => AuthCubit(getIt<AuthRepository>()),
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Cubits registered',
      ),
    );
  }

  /// Resets all dependencies (useful for testing).
  static Future<void> reset() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Resetting dependency injection',
      ),
    );

    // Close Hive service if registered
    if (getIt.isRegistered<HiveService>()) {
      await getIt<HiveService>().close();
    }

    // Reset GetIt
    await getIt.reset();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Dependency injection reset completed',
      ),
    );
  }
}
