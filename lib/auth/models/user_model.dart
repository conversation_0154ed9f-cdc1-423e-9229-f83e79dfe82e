import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'user_model.g.dart';

/// {@template user_model}
/// User model for authentication and local storage.
/// {@endtemplate}
@HiveType(typeId: 0)
class UserModel extends Equatable {
  /// {@macro user_model}
  const UserModel({
    required this.uid,
    required this.email,
    this.displayName,
    this.photoURL,
    this.emailVerified = false,
    this.createdAt,
    this.lastSignInAt,
  });

  /// Creates an empty user model.
  const UserModel.empty()
      : uid = '',
        email = '',
        displayName = null,
        photoURL = null,
        emailVerified = false,
        createdAt = null,
        lastSignInAt = null;

  /// User's unique identifier.
  @HiveField(0)
  final String uid;

  /// User's email address.
  @HiveField(1)
  final String email;

  /// User's display name.
  @HiveField(2)
  final String? displayName;

  /// User's profile photo URL.
  @HiveField(3)
  final String? photoURL;

  /// Whether the user's email is verified.
  @HiveField(4)
  final bool emailVerified;

  /// When the user account was created.
  @HiveField(5)
  final DateTime? createdAt;

  /// When the user last signed in.
  @HiveField(6)
  final DateTime? lastSignInAt;

  /// Whether this is an empty user (not authenticated).
  bool get isEmpty => uid.isEmpty;

  /// Whether this is a valid user (authenticated).
  bool get isNotEmpty => uid.isNotEmpty;

  /// Creates a copy of this user with the given fields replaced.
  UserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastSignInAt,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
    );
  }

  @override
  List<Object?> get props => [
        uid,
        email,
        displayName,
        photoURL,
        emailVerified,
        createdAt,
        lastSignInAt,
      ];

  @override
  String toString() {
    return 'UserModel(uid: $uid, email: $email, displayName: $displayName, '
        'emailVerified: $emailVerified)';
  }
}
