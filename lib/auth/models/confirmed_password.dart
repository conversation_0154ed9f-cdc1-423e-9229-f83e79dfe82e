import 'package:formz/formz.dart';

/// Validation errors for the [ConfirmedPassword] [FormzInput].
enum ConfirmedPasswordValidationError {
  /// Confirmation field is empty.
  empty,

  /// Passwords do not match.
  mismatch,
}

/// Extension to get user-friendly error messages
extension ConfirmedPasswordValidationErrorX
    on ConfirmedPasswordValidationError {
  String get message {
    switch (this) {
      case ConfirmedPasswordValidationError.empty:
        return 'Please confirm your password';
      case ConfirmedPasswordValidationError.mismatch:
        return 'Passwords do not match';
    }
  }
}

/// {@template confirmed_password}
/// Form input for a confirmed password input.
/// {@endtemplate}
class ConfirmedPassword
    extends FormzInput<String, ConfirmedPasswordValidationError> {
  /// {@macro confirmed_password}
  const ConfirmedPassword.pure({this.password = ''}) : super.pure('');

  /// {@macro confirmed_password}
  const ConfirmedPassword.dirty({
    required this.password,
    String value = '',
  }) : super.dirty(value);

  /// The original password.
  final String password;

  @override
  ConfirmedPasswordValidationError? validator(String? value) {
    if (value == null || value.isEmpty) {
      return ConfirmedPasswordValidationError.empty;
    }
    return password == value ? null : ConfirmedPasswordValidationError.mismatch;
  }
}
