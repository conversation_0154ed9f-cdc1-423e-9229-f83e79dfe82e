import 'package:formz/formz.dart';

/// Validation errors for the [Name] [FormzInput].
enum NameValidationError {
  /// Name field is empty.
  empty,

  /// Name is too short.
  tooShort,
}

/// Extension to get user-friendly error messages
extension NameValidationErrorX on NameValidationError {
  String get message {
    switch (this) {
      case NameValidationError.empty:
        return 'Please enter your full name';
      case NameValidationError.tooShort:
        return 'Name must be at least 2 characters long';
    }
  }
}

/// {@template name}
/// Form input for a name input.
/// {@endtemplate}
class Name extends FormzInput<String, NameValidationError> {
  /// {@macro name}
  const Name.pure() : super.pure('');

  /// {@macro name}
  const Name.dirty([super.value = '']) : super.dirty();

  @override
  NameValidationError? validator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return NameValidationError.empty;
    }
    return value.trim().length >= 2 ? null : NameValidationError.tooShort;
  }
}
