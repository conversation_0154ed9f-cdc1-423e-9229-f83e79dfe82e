import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'auth_state.dart';

/// {@template auth_cubit}
/// A [Cubit] which manages the global authentication state.
/// {@endtemplate}
class AuthCubit extends Cubit<AuthState> {
  /// {@macro auth_cubit}
  AuthCubit(this._authRepository) : super(const AuthState.unknown()) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'AuthCubit initialized',
      ),
    );

    // Listen to authentication state changes
    _userSubscription = _authRepository.user.listen(
      _onUserChanged,
      onError: _onUserError,
    );
  }

  final AuthRepository _authRepository;
  final LoggerService _logger = LoggerService();
  late final StreamSubscription<UserModel> _userSubscription;

  /// Handles user state changes from the repository.
  void _onUserChanged(UserModel user) {
    final status = user.isEmpty ? 'unauthenticated' : 'authenticated';
    final details = user.isEmpty ? 'User logged out' : 'User: ${user.email}';

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'User state changed: $status',
        details,
      ),
    );

    if (user.isEmpty) {
      emit(const AuthState.unauthenticated());
    } else {
      emit(AuthState.authenticated(user));
    }
  }

  /// Handles errors from the user stream.
  void _onUserError(Object error, StackTrace stackTrace) {
    _logger.error(
      LoggingConstants.formatError(
        LoggingConstants.authModule,
        LoggingConstants.criticalError,
        'Authentication stream error: $error',
        'User state monitoring failed',
      ),
      error,
      stackTrace,
    );

    // Emit unknown state on stream error
    emit(const AuthState.unknown());
  }

  /// Signs out the current user.
  Future<void> signOut() async {
    try {
      _logger.logAuth(
        LoggingConstants.logoutAttempt,
        'started',
        _authRepository.currentUser.email,
        'User initiated logout',
      );

      await _authRepository.logOut();

      _logger.logAuth(
        LoggingConstants.logoutSuccess,
        'completed',
        '',
        'User logout successful',
      );
    } catch (e, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Logout failed: $e',
          'User: ${_authRepository.currentUser.email}',
        ),
        e,
        stackTrace,
      );

      // Still emit unauthenticated state even if logout fails
      // to prevent user from being stuck in authenticated state
      emit(const AuthState.unauthenticated());
    }
  }

  /// Gets the current user.
  UserModel get currentUser => _authRepository.currentUser;

  /// Checks if the user is authenticated.
  bool get isAuthenticated => state.status == AuthStatus.authenticated;

  /// Checks if the user is unauthenticated.
  bool get isUnauthenticated => state.status == AuthStatus.unauthenticated;

  /// Checks if the authentication status is unknown.
  bool get isUnknown => state.status == AuthStatus.unknown;

  @override
  Future<void> close() {
    _userSubscription.cancel();
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'AuthCubit closed',
      ),
    );
    return super.close();
  }
}
