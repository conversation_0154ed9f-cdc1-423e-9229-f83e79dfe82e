part of 'login_cubit.dart';

/// {@template login_state}
/// The state of the login form.
/// {@endtemplate}
class LoginState extends Equatable {
  /// {@macro login_state}
  const LoginState({
    this.email = const Email.pure(),
    this.password = const Password.pure(),
    this.status = FormStatus.pure,
    this.errorMessage,
    this.emailTouched = false,
    this.passwordTouched = false,
  });

  /// The email input.
  final Email email;

  /// The password input.
  final Password password;

  /// The status of the form.
  final FormStatus status;

  /// The error message.
  final String? errorMessage;

  /// Whether the email field has been touched/focused.
  final bool emailTouched;

  /// Whether the password field has been touched/focused.
  final bool passwordTouched;

  /// Creates a copy of this state with the given fields replaced.
  LoginState copyWith({
    Email? email,
    Password? password,
    FormStatus? status,
    String? errorMessage,
    bool? emailTouched,
    bool? passwordTouched,
  }) {
    return LoginState(
      email: email ?? this.email,
      password: password ?? this.password,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      emailTouched: emailTouched ?? this.emailTouched,
      passwordTouched: passwordTouched ?? this.passwordTouched,
    );
  }

  @override
  List<Object?> get props => [
        email,
        password,
        status,
        errorMessage,
        emailTouched,
        passwordTouched,
      ];
}
