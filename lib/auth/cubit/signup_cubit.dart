import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/auth/models/confirmed_password.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/name.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'signup_state.dart';

/// {@template signup_cubit}
/// A [Cubit] which manages the signup form state.
/// {@endtemplate}
class SignupCubit extends Cubit<SignupState> {
  /// {@macro signup_cubit}
  SignupCubit(this._authRepository) : super(const SignupState()) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'SignupCubit initialized',
      ),
    );
  }

  final AuthRepository _authRepository;
  final LoggerService _logger = LoggerService();

  /// Updates the name input.
  void nameChanged(String value) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit nameChanged called after close',
          'Ignoring name change: $value',
        ),
      );
      return;
    }

    final name = Name.dirty(value);
    emit(
      state.copyWith(
        name: name,
        status: _getFormStatus(
          name,
          state.email,
          state.password,
          state.confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the email input.
  void emailChanged(String value) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit emailChanged called after close',
          'Ignoring email change: $value',
        ),
      );
      return;
    }

    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: _getFormStatus(
          state.name,
          email,
          state.password,
          state.confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the password input.
  void passwordChanged(String value) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit passwordChanged called after close',
          'Ignoring password change: ${value.length} chars',
        ),
      );
      return;
    }

    final password = Password.dirty(value);
    final confirmedPassword = ConfirmedPassword.dirty(
      password: password.value,
      value: state.confirmedPassword.value,
    );
    emit(
      state.copyWith(
        password: password,
        confirmedPassword: confirmedPassword,
        status: _getFormStatus(
          state.name,
          state.email,
          password,
          confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the confirmed password input.
  void confirmedPasswordChanged(String value) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit confirmedPasswordChanged called after close',
          'Ignoring confirmed password change: ${value.length} chars',
        ),
      );
      return;
    }

    final confirmedPassword = ConfirmedPassword.dirty(
      password: state.password.value,
      value: value,
    );
    emit(
      state.copyWith(
        confirmedPassword: confirmedPassword,
        status: _getFormStatus(
          state.name,
          state.email,
          state.password,
          confirmedPassword,
        ),
      ),
    );
  }

  /// Marks the name field as touched.
  void nameTouched() {
    if (isClosed) return;
    emit(state.copyWith(nameTouched: true));
  }

  /// Marks the email field as touched.
  void emailTouched() {
    if (isClosed) return;
    emit(state.copyWith(emailTouched: true));
  }

  /// Marks the password field as touched.
  void passwordTouched() {
    if (isClosed) return;
    emit(state.copyWith(passwordTouched: true));
  }

  /// Marks the confirmed password field as touched.
  void confirmedPasswordTouched() {
    if (isClosed) return;
    emit(state.copyWith(confirmedPasswordTouched: true));
  }

  /// Gets the form status based on validation
  FormStatus _getFormStatus(
    Name name,
    Email email,
    Password password,
    ConfirmedPassword confirmedPassword,
  ) {
    return name.isValid &&
            email.isValid &&
            password.isValid &&
            confirmedPassword.isValid
        ? FormStatus.valid
        : FormStatus.invalid;
  }

  /// Submits the signup form.
  Future<void> signUpFormSubmitted() async {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit signUpFormSubmitted called after close',
        ),
      );
      return;
    }

    if (!state.status.isValidated) {
      _logger.logAuth(
        LoggingConstants.signupAttempt,
        'blocked',
        state.email.value,
        'Form validation failed',
      );
      return;
    }

    final email = state.email.value;
    final name = state.name.value;
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.signupAttempt,
      'started',
      email,
      'Name: $name, Form validation passed',
    );

    if (isClosed) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.signUp(
        email: email,
        password: state.password.value,
        name: name,
      );

      // Check if cubit is still active before proceeding
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during registration',
            'Skipping success state emission for: $email',
          ),
        );
        return;
      }

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Signup registration',
          duration,
          'Email: $email, Name: $name',
        )
        ..logAuth(
          LoggingConstants.signupSuccess,
          'completed',
          email,
          'Registration successful for: $name',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionSuccess));
      }
    } on SignUpWithEmailAndPasswordFailure catch (e) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during registration error',
            'Skipping error state emission for: $email',
          ),
        );
        return;
      }

      _logger.logAuth(
        LoggingConstants.signupFailure,
        'failed',
        email,
        'Registration error: ${e.message}',
      );

      if (!isClosed) {
        emit(
          state.copyWith(
            errorMessage: e.message,
            status: FormStatus.submissionFailure,
          ),
        );
      }
    } catch (error, stackTrace) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during unexpected error',
            'Skipping error state emission for: $email',
          ),
        );
        return;
      }

      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Signup process - Email: $email, Name: $name',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.signupFailure,
          'error',
          email,
          'Unexpected error during registration',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionFailure));
      }
    }
  }

  /// Submits Google Sign-In authentication.
  Future<void> signUpWithGoogle() async {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit signUpWithGoogle called after close',
        ),
      );
      return;
    }

    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.signupAttempt,
      'started',
      'google_sign_in',
      'Google Sign-In registration initiated',
    );

    if (isClosed) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.logInWithGoogle();

      // Check if cubit is still active before proceeding
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during Google authentication',
            'Skipping success state emission',
          ),
        );
        return;
      }

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Google Sign-In registration',
          duration,
          'Google authentication',
        )
        ..logAuth(
          LoggingConstants.signupSuccess,
          'completed',
          'google_sign_in',
          'Google registration successful',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionSuccess));
      }
    } on LogInWithGoogleFailure catch (e) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during Google authentication error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger.logAuth(
        LoggingConstants.signupFailure,
        'failed',
        'google_sign_in',
        'Google registration error: ${e.message}',
      );

      if (!isClosed) {
        emit(
          state.copyWith(
            errorMessage: e.message,
            status: FormStatus.submissionFailure,
          ),
        );
      }
    } catch (error, stackTrace) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during unexpected Google error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Google Sign-In registration process',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.signupFailure,
          'error',
          'google_sign_in',
          'Unexpected error during Google registration',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionFailure));
      }
    }
  }

  /// Submits Apple Sign-In authentication.
  Future<void> signUpWithApple() async {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'SignupCubit signUpWithApple called after close',
        ),
      );
      return;
    }

    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.signupAttempt,
      'started',
      'apple_sign_in',
      'Apple Sign-In registration initiated',
    );

    if (isClosed) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.logInWithApple();

      // Check if cubit is still active before proceeding
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during Apple authentication',
            'Skipping success state emission',
          ),
        );
        return;
      }

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Apple Sign-In registration',
          duration,
          'Apple authentication',
        )
        ..logAuth(
          LoggingConstants.signupSuccess,
          'completed',
          'apple_sign_in',
          'Apple registration successful',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionSuccess));
      }
    } on LogInWithAppleFailure catch (e) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during Apple authentication error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger.logAuth(
        LoggingConstants.signupFailure,
        'failed',
        'apple_sign_in',
        'Apple registration error: ${e.message}',
      );

      if (!isClosed) {
        emit(
          state.copyWith(
            errorMessage: e.message,
            status: FormStatus.submissionFailure,
          ),
        );
      }
    } catch (error, stackTrace) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'SignupCubit closed during unexpected Apple error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Apple Sign-In registration process',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.signupFailure,
          'error',
          'apple_sign_in',
          'Unexpected error during Apple registration',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionFailure));
      }
    }
  }

  @override
  Future<void> close() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'SignupCubit closed',
        'Email: ${state.email.value}',
      ),
    );
    return super.close();
  }
}
