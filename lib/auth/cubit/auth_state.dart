part of 'auth_cubit.dart';

/// Enum representing the authentication status.
enum AuthStatus {
  /// Authentication status is unknown.
  unknown,

  /// User is authenticated.
  authenticated,

  /// User is not authenticated.
  unauthenticated,
}

/// {@template auth_state}
/// The state of the global authentication.
/// {@endtemplate}
class AuthState extends Equatable {
  /// {@macro auth_state}
  const AuthState._({
    required this.status,
    this.user = const UserModel.empty(),
  });

  /// Creates an unknown authentication state.
  const AuthState.unknown() : this._(status: AuthStatus.unknown);

  /// Creates an authenticated state with the given [user].
  const AuthState.authenticated(UserModel user)
      : this._(status: AuthStatus.authenticated, user: user);

  /// Creates an unauthenticated state.
  const AuthState.unauthenticated()
      : this._(status: AuthStatus.unauthenticated);

  /// The authentication status.
  final AuthStatus status;

  /// The current user.
  final UserModel user;

  @override
  List<Object> get props => [status, user];

  @override
  String toString() {
    return 'AuthState(status: $status, user: ${user.email})';
  }
}
