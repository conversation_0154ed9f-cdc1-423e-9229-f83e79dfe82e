import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'forgot_password_state.dart';

/// {@template forgot_password_cubit}
/// A [Cubit] which manages the forgot password form state.
/// {@endtemplate}
class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  /// {@macro forgot_password_cubit}
  ForgotPasswordCubit(this._authRepository)
      : super(const ForgotPasswordState()) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'ForgotPasswordCubit initialized',
      ),
    );
  }

  final AuthRepository _authRepository;
  final LoggerService _logger = LoggerService();

  /// Updates the email input.
  void emailChanged(String value) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'ForgotPasswordCubit emailChanged called after close',
          'Ignoring email change: $value',
        ),
      );
      return;
    }

    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: email.isValid ? FormStatus.valid : FormStatus.invalid,
      ),
    );
  }

  /// Marks the email field as touched.
  void emailTouched() {
    if (isClosed) return;
    emit(state.copyWith(emailTouched: true));
  }

  /// Submits the forgot password form.
  Future<void> sendPasswordResetEmail() async {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'ForgotPasswordCubit sendPasswordResetEmail called after close',
        ),
      );
      return;
    }

    if (!state.status.isValidated) {
      _logger.logAuth(
        LoggingConstants.passwordResetAttempt,
        'blocked',
        state.email.value,
        'Form validation failed',
      );
      return;
    }

    final email = state.email.value;
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.passwordResetAttempt,
      'started',
      email,
      'Form validation passed',
    );

    if (isClosed) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.sendPasswordResetEmail(email: email);

      // Check if cubit is still active before proceeding
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'ForgotPasswordCubit closed during password reset',
            'Skipping success state emission for: $email',
          ),
        );
        return;
      }

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Password reset email',
          duration,
          'Email: $email',
        )
        ..logAuth(
          LoggingConstants.passwordResetSuccess,
          'completed',
          email,
          'Reset email sent successfully',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionSuccess));
      }
    } on SendPasswordResetEmailFailure catch (e) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'ForgotPasswordCubit closed during password reset error',
            'Skipping error state emission for: $email',
          ),
        );
        return;
      }

      _logger.logAuth(
        LoggingConstants.passwordResetFailure,
        'failed',
        email,
        'Reset email error: ${e.message}',
      );

      if (!isClosed) {
        emit(
          state.copyWith(
            errorMessage: e.message,
            status: FormStatus.submissionFailure,
          ),
        );
      }
    } catch (error, stackTrace) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'ForgotPasswordCubit closed during unexpected error',
            'Skipping error state emission for: $email',
          ),
        );
        return;
      }

      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Password reset process - Email: $email',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.passwordResetFailure,
          'error',
          email,
          'Unexpected error during password reset',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionFailure));
      }
    }
  }

  @override
  Future<void> close() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'ForgotPasswordCubit closed',
        'Email: ${state.email.value}',
      ),
    );
    return super.close();
  }
}
