import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'login_state.dart';

/// {@template login_cubit}
/// A [Cubit] which manages the login form state.
/// {@endtemplate}
class LoginCubit extends Cubit<LoginState> {
  /// {@macro login_cubit}
  LoginCubit(this._authRepository) : super(const LoginState()) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'LoginCubit initialized',
      ),
    );
  }

  final AuthRepository _authRepository;
  final LoggerService _logger = LoggerService();

  /// Updates the email input.
  void emailChanged(String value) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit emailChanged called after close',
          'Ignoring email change: $value',
        ),
      );
      return;
    }

    _logger.logFormValidation(
      'LoginForm',
      'email',
      'changed',
      'Length: ${value.length}',
    );

    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: _getFormStatus(email, state.password),
      ),
    );

    if (!email.isValid && value.isNotEmpty) {
      _logger.logFormValidation(
        'LoginForm',
        'email',
        'validation_failed',
        'Invalid email format',
      );
    }
  }

  /// Updates the password input.
  void passwordChanged(String value) {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit passwordChanged called after close',
          'Ignoring password change: ${value.length} chars',
        ),
      );
      return;
    }

    _logger.logFormValidation(
      'LoginForm',
      'password',
      'changed',
      'Length: ${value.length}',
    );

    final password = Password.dirty(value);
    emit(
      state.copyWith(
        password: password,
        status: _getFormStatus(state.email, password),
      ),
    );

    if (!password.isValid && value.isNotEmpty) {
      _logger.logFormValidation(
        'LoginForm',
        'password',
        'validation_failed',
        'Password does not meet requirements',
      );
    }
  }

  /// Marks the email field as touched.
  void emailTouched() {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit emailTouched called after close',
        ),
      );
      return;
    }
    _logger.logFormValidation('LoginForm', 'email', 'touched');
    emit(state.copyWith(emailTouched: true));
  }

  /// Marks the password field as touched.
  void passwordTouched() {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit passwordTouched called after close',
        ),
      );
      return;
    }
    _logger.logFormValidation('LoginForm', 'password', 'touched');
    emit(state.copyWith(passwordTouched: true));
  }

  /// Gets the form status based on validation
  FormStatus _getFormStatus(Email email, Password password) {
    return email.isValid && password.isValid
        ? FormStatus.valid
        : FormStatus.invalid;
  }

  /// Submits the login form.
  Future<void> logInWithCredentials() async {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit logInWithCredentials called after close',
        ),
      );
      return;
    }

    if (!state.status.isValidated) {
      _logger.logAuth(
        LoggingConstants.loginAttempt,
        'blocked',
        state.email.value,
        'Form validation failed',
      );
      return;
    }

    final email = state.email.value;
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.loginAttempt,
      'started',
      email,
      'Form validation passed',
    );

    if (isClosed) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.logInWithEmailAndPassword(
        email: email,
        password: state.password.value,
      );

      // Check if cubit is still active before proceeding
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during authentication',
            'Skipping success state emission for: $email',
          ),
        );
        return;
      }

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Login authentication',
          duration,
          'Email: $email',
        )
        ..logAuth(
          LoggingConstants.loginSuccess,
          'completed',
          email,
          'Authentication successful',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionSuccess));
      }
    } on LogInWithEmailAndPasswordFailure catch (e) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during authentication error',
            'Skipping error state emission for: $email',
          ),
        );
        return;
      }

      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        email,
        'Authentication error: ${e.message}',
      );

      if (!isClosed) {
        emit(
          state.copyWith(
            errorMessage: e.message,
            status: FormStatus.submissionFailure,
          ),
        );
      }
    } catch (error, stackTrace) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during unexpected error',
            'Skipping error state emission for: $email',
          ),
        );
        return;
      }

      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Login process - Email: $email',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.loginFailure,
          'error',
          email,
          'Unexpected error during authentication',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionFailure));
      }
    }
  }

  /// Submits Google Sign-In authentication.
  Future<void> logInWithGoogle() async {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit logInWithGoogle called after close',
        ),
      );
      return;
    }

    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.loginAttempt,
      'started',
      'google_sign_in',
      'Google Sign-In initiated',
    );

    if (isClosed) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.logInWithGoogle();

      // Check if cubit is still active before proceeding
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during Google authentication',
            'Skipping success state emission',
          ),
        );
        return;
      }

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Google Sign-In authentication',
          duration,
          'Google authentication',
        )
        ..logAuth(
          LoggingConstants.loginSuccess,
          'completed',
          'google_sign_in',
          'Google authentication successful',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionSuccess));
      }
    } on LogInWithGoogleFailure catch (e) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during Google authentication error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        'google_sign_in',
        'Google authentication error: ${e.message}',
      );

      if (!isClosed) {
        emit(
          state.copyWith(
            errorMessage: e.message,
            status: FormStatus.submissionFailure,
          ),
        );
      }
    } catch (error, stackTrace) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during unexpected Google error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Google Sign-In process',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.loginFailure,
          'error',
          'google_sign_in',
          'Unexpected error during Google authentication',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionFailure));
      }
    }
  }

  /// Submits Apple Sign-In authentication.
  Future<void> logInWithApple() async {
    if (isClosed) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'LoginCubit logInWithApple called after close',
        ),
      );
      return;
    }

    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.loginAttempt,
      'started',
      'apple_sign_in',
      'Apple Sign-In initiated',
    );

    if (isClosed) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));

    try {
      await _authRepository.logInWithApple();

      // Check if cubit is still active before proceeding
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during Apple authentication',
            'Skipping success state emission',
          ),
        );
        return;
      }

      final duration = DateTime.now().difference(startTime);
      _logger
        ..logPerformance(
          'Apple Sign-In authentication',
          duration,
          'Apple authentication',
        )
        ..logAuth(
          LoggingConstants.loginSuccess,
          'completed',
          'apple_sign_in',
          'Apple authentication successful',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionSuccess));
      }
    } on LogInWithAppleFailure catch (e) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during Apple authentication error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        'apple_sign_in',
        'Apple authentication error: ${e.message}',
      );

      if (!isClosed) {
        emit(
          state.copyWith(
            errorMessage: e.message,
            status: FormStatus.submissionFailure,
          ),
        );
      }
    } catch (error, stackTrace) {
      if (isClosed) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'LoginCubit closed during unexpected Apple error',
            'Skipping error state emission',
          ),
        );
        return;
      }

      _logger
        ..error(
          LoggingConstants.formatError(
            LoggingConstants.authModule,
            LoggingConstants.authenticationFailure,
            error.toString(),
            'Apple Sign-In process',
          ),
          error,
          stackTrace,
        )
        ..logAuth(
          LoggingConstants.loginFailure,
          'error',
          'apple_sign_in',
          'Unexpected error during Apple authentication',
        );

      if (!isClosed) {
        emit(state.copyWith(status: FormStatus.submissionFailure));
      }
    }
  }

  @override
  Future<void> close() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.authModule,
        'LoginCubit closed',
        'Email: ${state.email.value}',
      ),
    );
    return super.close();
  }
}
