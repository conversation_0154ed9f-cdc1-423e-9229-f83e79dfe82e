import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// {@template logger_service}
/// Centralized logging service for the Bloomg Flutter application.
///
/// Provides environment-specific logging configuration:
/// - Development: Full logging with colors, emojis, and stack traces
/// - Staging: Structured logging with reduced verbosity
/// - Production: Minimal logging (warnings and errors only)
/// {@endtemplate}
class LoggerService {
  /// Factory constructor that returns the singleton instance
  factory LoggerService() {
    _instance ??= LoggerService._internal();
    return _instance!;
  }

  /// Private constructor for singleton pattern
  LoggerService._internal() {
    _logger = _createLogger();
  }
  static LoggerService? _instance;
  late final Logger _logger;

  /// Gets the logger instance
  Logger get logger => _logger;

  /// Creates a logger with environment-specific configuration
  Logger _createLogger() {
    final level = _getLogLevel();
    final printer = _getPrinter();
    final output = _getOutput();

    return Logger(
      level: level,
      printer: printer,
      output: output,
      filter: ProductionFilter(),
    );
  }

  /// Determines the appropriate log level based on the environment
  Level _getLogLevel() {
    if (kDebugMode) {
      // Development: Log everything for debugging
      return Level.trace;
    } else if (_isStaging()) {
      // Staging: Log info and above for testing
      return Level.info;
    } else {
      // Production: Only warnings and errors
      return Level.warning;
    }
  }

  /// Creates the appropriate printer based on the environment
  LogPrinter _getPrinter() {
    if (kDebugMode) {
      // Development: Pretty printer with colors and emojis
      return PrettyPrinter(
        methodCount: 3,
        lineLength: 80,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      );
    } else if (_isStaging()) {
      // Staging: Simplified pretty printer
      return PrettyPrinter(
        methodCount: 1,
        errorMethodCount: 3,
        colors: false,
        printEmojis: false,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        excludeBox: const {
          Level.info: false,
          Level.debug: false,
          Level.trace: false,
        },
        noBoxingByDefault: true,
      );
    } else {
      // Production: Simple printer for minimal overhead
      return SimplePrinter(
        colors: false,
      );
    }
  }

  /// Creates the appropriate output based on the environment
  LogOutput _getOutput() {
    if (kDebugMode) {
      // Development: Console output
      return ConsoleOutput();
    } else {
      // Staging/Production: Multi-output (console + file if needed)
      return MultiOutput([
        ConsoleOutput(),
        // Add file output for production if needed
        // FileOutput(file: File('logs/app.log')),
      ]);
    }
  }

  /// Checks if the app is running in staging environment
  bool _isStaging() {
    // This can be determined by build flavor or environment variables
    // For now, we'll use a simple check based on the app name or other
    // indicators
    return const String.fromEnvironment('FLAVOR') == 'staging';
  }

  // Convenience methods for different log levels

  /// Logs a trace message (development only)
  void trace(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.t(message, error: error, stackTrace: stackTrace);
  }

  /// Logs a debug message (development only)
  void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Logs an info message
  void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Logs a warning message
  void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Logs an error message
  void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Logs a fatal error message
  void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  // Structured logging methods for specific use cases

  /// Logs user actions with structured format
  void logUserAction(
    String action,
    String userId, [
    Map<String, dynamic>? metadata,
  ]) {
    final message = _formatUserAction(action, userId, metadata);
    info(message);
  }

  /// Logs performance metrics
  void logPerformance(String operation, Duration duration, [String? details]) {
    final message = _formatPerformance(operation, duration, details);
    info(message);
  }

  /// Logs security events
  void logSecurity(String event, String level, [String? details]) {
    final message = _formatSecurity(event, level, details);
    if (level.toLowerCase() == 'critical' || level.toLowerCase() == 'high') {
      error(message);
    } else {
      warning(message);
    }
  }

  /// Logs authentication events
  void logAuth(
    String action,
    String result, [
    String? userId,
    String? details,
  ]) {
    final message = '[AUTH] $action: $result';
    final fullMessage = userId != null
        ? '$message - User: $userId${details != null ? ' ($details)' : ''}'
        : '$message${details != null ? ' - $details' : ''}';

    if (result.toLowerCase().contains('success')) {
      info(fullMessage);
    } else if (result.toLowerCase().contains('failed') ||
        result.toLowerCase().contains('error')) {
      warning(fullMessage);
    } else {
      debug(fullMessage);
    }
  }

  /// Logs Firebase operations
  void logFirebase(String operation, String result, [String? details]) {
    final message = '[FIREBASE] $operation: $result';
    final fullMessage = details != null ? '$message - $details' : message;

    if (result.toLowerCase().contains('success')) {
      debug(fullMessage);
    } else if (result.toLowerCase().contains('error') ||
        result.toLowerCase().contains('failed')) {
      error(fullMessage);
    } else {
      info(fullMessage);
    }
  }

  /// Logs BLoC state changes
  void logBlocChange(
    String blocName,
    String fromState,
    String toState, [
    String? event,
  ]) {
    final message = '[BLOC] $blocName: $fromState → $toState';
    final fullMessage = event != null ? '$message (Event: $event)' : message;
    trace(fullMessage);
  }

  /// Logs BLoC errors
  void logBlocError(String blocName, dynamic error, [StackTrace? stackTrace]) {
    final message = '[BLOC] Error in $blocName: $error';
    this.error(message, error, stackTrace);
  }

  /// Logs navigation events
  void logNavigation(
    String action,
    String route, [
    Map<String, dynamic>? arguments,
  ]) {
    final message = '[NAVIGATION] $action: $route';
    final fullMessage = arguments != null && arguments.isNotEmpty
        ? '$message (Args: $arguments)'
        : message;
    debug(fullMessage);
  }

  /// Logs form validation events
  void logFormValidation(
    String formName,
    String field,
    String result, [
    String? error,
  ]) {
    final message = '[FORM] $formName.$field: $result';
    final fullMessage = error != null ? '$message - $error' : message;

    if (result.toLowerCase().contains('error') ||
        result.toLowerCase().contains('invalid')) {
      debug(fullMessage);
    } else {
      trace(fullMessage);
    }
  }

  // Private helper methods for message formatting

  String _formatUserAction(
    String action,
    String userId,
    Map<String, dynamic>? metadata,
  ) {
    final baseMessage = '[USER] $action - User: $userId';
    if (metadata != null && metadata.isNotEmpty) {
      final metadataStr =
          metadata.entries.map((e) => '${e.key}: ${e.value}').join(', ');
      return '$baseMessage (Metadata: $metadataStr)';
    }
    return baseMessage;
  }

  String _formatPerformance(
    String operation,
    Duration duration,
    String? details,
  ) {
    final baseMessage =
        '[PERFORMANCE] $operation completed in ${duration.inMilliseconds}ms';
    return details != null ? '$baseMessage - $details' : baseMessage;
  }

  String _formatSecurity(String event, String level, String? details) {
    final baseMessage = '[SECURITY] $level: $event';
    return details != null ? '$baseMessage - $details' : baseMessage;
  }
}

/// Custom filter for production environments
class ProductionFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    if (kDebugMode) {
      // In debug mode, log everything
      return true;
    }

    // In production, only log warnings and above
    return event.level.index >= Level.warning.index;
  }
}
