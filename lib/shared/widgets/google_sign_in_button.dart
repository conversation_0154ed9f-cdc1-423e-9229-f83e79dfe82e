import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Added import for SVG support

// Official Google G Logo SVG data
const String _googleLogoSvg = '''
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
  <path fill="#4285F4" d="M17.64,9.20454545 C17.64,8.56636364 17.5827273,7.95272727 17.4763636,7.36363636 L9,7.36363636 L9,10.845 L13.8436364,10.845 C13.635,11.97 13.0009091,12.9231818 12.0477273,13.5613636 L12.0477273,15.8195455 L14.9563636,15.8195455 C16.6581818,14.2527273 17.64,11.9454545 17.64,9.20454545 L17.64,9.20454545 Z"></path>
  <path fill="#34A853" d="M9,18 C11.43,18 13.4672727,17.1940909 14.9563636,15.8195455 L12.0477273,13.5613636 C11.2418182,14.1231818 10.2109091,14.4545455 9,14.4545455 C6.65590909,14.4545455 4.67181818,12.8372727 3.96409091,10.71 L0.957272727,10.71 L0.957272727,13.0418182 C2.43818182,15.9831818 5.48181818,18 9,18 Z"></path>
  <path fill="#FBBC05" d="M3.96409091,10.71 C3.78409091,10.1731818 3.68181818,9.59318182 3.68181818,9 C3.68181818,8.40681818 3.78409091,7.82681818 3.96409091,7.29 L0.957272727,4.95818182 C0.347727273,6.17318182 0,7.54772727 0,9 C0,10.4522727 0.347727273,11.8268182 0.957272727,13.0418182 L3.96409091,10.71 Z"></path>
  <path fill="#EA4335" d="M9,3.54545455 C10.3213636,3.54545455 11.5077273,4.00136364 12.4404545,4.88545455 L15.0218182,2.34681818 C13.4631818,0.891818182 11.4259091,0 9,0 C5.48181818,0 2.43818182,2.01681818 0.957272727,4.95818182 L3.96409091,7.29 C4.67181818,5.16272727 6.65590909,3.54545455 9,3.54545455 Z"></path>
</svg>''';

/// Google Sign-In button widget following Material Design guidelines
class GoogleSignInButton extends StatelessWidget {
  /// Creates a Google Sign-In button.
  const GoogleSignInButton({
    required this.onPressed,
    this.isLoading = false,
    super.key,
  });

  /// Callback function when the button is pressed.
  final VoidCallback? onPressed;

  /// Whether the button is in loading state.
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.socialSignInButtonHeight,
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.white,
          side: BorderSide(
            color: Colors.grey.shade300,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          elevation: 0,
          shadowColor: Colors.transparent,
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.textSecondary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Google logo
                  SvgPicture.string(
                    _googleLogoSvg,
                    width: 18,
                    height: 18,
                    semanticsLabel: 'Google logo',
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Text(
                    'Continue with Google',
                    style: AppTextStyles.button.copyWith(
                      color: Colors.black87,
                    ), // Ensured dark text for contrast
                  ),
                ],
              ),
      ),
    );
  }
}
