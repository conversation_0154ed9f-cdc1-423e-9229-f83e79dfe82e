/// Enum representing the status of a form submission
enum FormStatus {
  /// Initial state
  pure,

  /// Form is valid and ready for submission
  valid,

  /// Form is invalid
  invalid,

  /// Form submission is in progress
  submissionInProgress,

  /// Form submission was successful
  submissionSuccess,

  /// Form submission failed
  submissionFailure;

  /// Whether the form is valid and ready for submission
  bool get isValidated => this == FormStatus.valid;

  /// Whether the form submission is in progress
  bool get isSubmissionInProgress => this == FormStatus.submissionInProgress;

  /// Whether the form submission was successful
  bool get isSubmissionSuccess => this == FormStatus.submissionSuccess;

  /// Whether the form submission failed
  bool get isSubmissionFailure => this == FormStatus.submissionFailure;
}
