import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'video_gallery_event.dart';
part 'video_gallery_state.dart';

/// {@template video_gallery_bloc}
/// BLoC that manages the video gallery state and operations.
///
/// Handles loading videos, applying filters, deleting videos,
/// and managing the overall gallery state.
/// {@endtemplate}
class VideoGalleryBloc extends Bloc<VideoGalleryEvent, VideoGalleryState> {
  /// {@macro video_gallery_bloc}
  VideoGalleryBloc({
    required VideoGalleryRepository repository,
  })  : _repository = repository,
        super(const VideoGalleryInitial()) {
    // Register event handlers
    on<LoadVideos>(_onLoadVideos);
    on<RefreshVideos>(_onRefreshVideos);
    on<DeleteVideo>(_onDeleteVideo);
    on<ApplyFilter>(_onApplyFilter);
    on<ClearFilter>(_onClearFilter);
    on<GenerateThumbnail>(_onGenerateThumbnail);
    on<LoadStorageStats>(_onLoadStorageStats);
    on<VideoSelected>(_onVideoSelected);
    on<VideoDeselected>(_onVideoDeselected);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'VideoGalleryBloc initialized',
      ),
    );
  }

  final VideoGalleryRepository _repository;
  final LoggerService _logger = LoggerService();

  /// Handles loading videos from storage
  Future<void> _onLoadVideos(
    LoadVideos event,
    Emitter<VideoGalleryState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Loading videos',
      ),
    );

    emit(
      VideoGalleryLoading(
        allVideos: state.allVideos,
        filteredVideos: state.filteredVideos,
        currentFilter: state.currentFilter,
        selectedVideo: state.selectedVideo,
        storageStats: state.storageStats,
      ),
    );

    try {
      final videos = await _repository.loadVideos();

      if (videos.isEmpty) {
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'No videos found',
          ),
        );

        emit(
          VideoGalleryEmpty(
            currentFilter: state.currentFilter,
            storageStats: state.storageStats,
          ),
        );
      } else {
        final filteredVideos =
            _repository.filterVideos(videos, state.currentFilter);

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Videos loaded successfully',
            'Total: ${videos.length}, Filtered: ${filteredVideos.length}',
          ),
        );

        emit(
          VideoGalleryLoaded(
            allVideos: videos,
            filteredVideos: filteredVideos,
            currentFilter: state.currentFilter,
            selectedVideo: state.selectedVideo,
            storageStats: state.storageStats,
          ),
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to load videos: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        VideoGalleryError(
          message: 'Failed to load videos: $error',
          allVideos: state.allVideos,
          filteredVideos: state.filteredVideos,
          currentFilter: state.currentFilter,
          selectedVideo: state.selectedVideo,
          storageStats: state.storageStats,
        ),
      );
    }
  }

  /// Handles refreshing videos from storage
  Future<void> _onRefreshVideos(
    RefreshVideos event,
    Emitter<VideoGalleryState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Refreshing videos',
      ),
    );

    // Trigger load videos which will handle the refresh
    add(const LoadVideos());
  }

  /// Handles deleting a video
  Future<void> _onDeleteVideo(
    DeleteVideo event,
    Emitter<VideoGalleryState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Deleting video',
        'ID: ${event.video.id}',
      ),
    );

    emit(
      VideoDeleting(
        deletingVideo: event.video,
        allVideos: state.allVideos,
        filteredVideos: state.filteredVideos,
        currentFilter: state.currentFilter,
        selectedVideo: state.selectedVideo,
        storageStats: state.storageStats,
      ),
    );

    try {
      final success = await _repository.deleteVideo(event.video);

      if (success) {
        // Remove video from lists
        final updatedAllVideos = state.allVideos
            .where((video) => video.id != event.video.id)
            .toList();
        final updatedFilteredVideos = state.filteredVideos
            .where((video) => video.id != event.video.id)
            .toList();

        // Clear selection if deleted video was selected
        final updatedSelectedVideo = state.selectedVideo?.id == event.video.id
            ? null
            : state.selectedVideo;

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video deleted successfully',
            'ID: ${event.video.id}',
          ),
        );

        if (updatedAllVideos.isEmpty) {
          emit(
            VideoGalleryEmpty(
              currentFilter: state.currentFilter,
              storageStats: state.storageStats,
            ),
          );
        } else {
          emit(
            VideoDeleted(
              deletedVideo: event.video,
              allVideos: updatedAllVideos,
              filteredVideos: updatedFilteredVideos,
              currentFilter: state.currentFilter,
              selectedVideo: updatedSelectedVideo,
              storageStats: state.storageStats,
            ),
          );
        }
      } else {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video deletion failed',
            'ID: ${event.video.id}',
          ),
        );

        emit(
          VideoDeleteError(
            video: event.video,
            message: 'Failed to delete video',
            allVideos: state.allVideos,
            filteredVideos: state.filteredVideos,
            currentFilter: state.currentFilter,
            selectedVideo: state.selectedVideo,
            storageStats: state.storageStats,
          ),
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Video deletion error: $error',
          'ID: ${event.video.id}',
        ),
        error,
        stackTrace,
      );

      emit(
        VideoDeleteError(
          video: event.video,
          message: 'Error deleting video: $error',
          allVideos: state.allVideos,
          filteredVideos: state.filteredVideos,
          currentFilter: state.currentFilter,
          selectedVideo: state.selectedVideo,
          storageStats: state.storageStats,
        ),
      );
    }
  }

  /// Handles applying a filter to the video list
  void _onApplyFilter(
    ApplyFilter event,
    Emitter<VideoGalleryState> emit,
  ) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Applying filter',
        'Filter: ${event.filter}',
      ),
    );

    final filteredVideos =
        _repository.filterVideos(state.allVideos, event.filter);

    if (state.allVideos.isEmpty) {
      emit(
        VideoGalleryEmpty(
          currentFilter: event.filter,
          storageStats: state.storageStats,
        ),
      );
    } else {
      emit(
        VideoGalleryLoaded(
          allVideos: state.allVideos,
          filteredVideos: filteredVideos,
          currentFilter: event.filter,
          selectedVideo: state.selectedVideo,
          storageStats: state.storageStats,
        ),
      );
    }
  }

  /// Handles clearing all filters
  void _onClearFilter(
    ClearFilter event,
    Emitter<VideoGalleryState> emit,
  ) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Clearing filters',
      ),
    );

    add(const ApplyFilter(GalleryFilter()));
  }

  /// Handles generating a thumbnail for a video
  Future<void> _onGenerateThumbnail(
    GenerateThumbnail event,
    Emitter<VideoGalleryState> emit,
  ) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Generating thumbnail',
        'Video ID: ${event.video.id}',
      ),
    );

    emit(
      ThumbnailGenerating(
        video: event.video,
        allVideos: state.allVideos,
        filteredVideos: state.filteredVideos,
        currentFilter: state.currentFilter,
        selectedVideo: state.selectedVideo,
        storageStats: state.storageStats,
      ),
    );

    try {
      final updatedVideo = await _repository.generateThumbnail(event.video);

      if (updatedVideo != null) {
        // Update video in lists
        final updatedAllVideos = state.allVideos.map((video) {
          return video.id == event.video.id ? updatedVideo : video;
        }).toList();

        final updatedFilteredVideos = state.filteredVideos.map((video) {
          return video.id == event.video.id ? updatedVideo : video;
        }).toList();

        emit(
          VideoGalleryLoaded(
            allVideos: updatedAllVideos,
            filteredVideos: updatedFilteredVideos,
            currentFilter: state.currentFilter,
            selectedVideo: state.selectedVideo,
            storageStats: state.storageStats,
          ),
        );
      } else {
        // Return to previous state if thumbnail generation failed
        emit(
          VideoGalleryLoaded(
            allVideos: state.allVideos,
            filteredVideos: state.filteredVideos,
            currentFilter: state.currentFilter,
            selectedVideo: state.selectedVideo,
            storageStats: state.storageStats,
          ),
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Thumbnail generation error: $error',
          'Video ID: ${event.video.id}',
        ),
        error,
        stackTrace,
      );

      // Return to previous state on error
      emit(
        VideoGalleryLoaded(
          allVideos: state.allVideos,
          filteredVideos: state.filteredVideos,
          currentFilter: state.currentFilter,
          selectedVideo: state.selectedVideo,
          storageStats: state.storageStats,
        ),
      );
    }
  }

  /// Handles loading storage statistics
  Future<void> _onLoadStorageStats(
    LoadStorageStats event,
    Emitter<VideoGalleryState> emit,
  ) async {
    try {
      final stats = await _repository.getStorageStats();

      emit(
        VideoGalleryLoaded(
          allVideos: state.allVideos,
          filteredVideos: state.filteredVideos,
          currentFilter: state.currentFilter,
          selectedVideo: state.selectedVideo,
          storageStats: stats,
        ),
      );
    } catch (error) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to load storage stats: $error',
        ),
        error,
      );
    }
  }

  /// Handles video selection
  void _onVideoSelected(
    VideoSelected event,
    Emitter<VideoGalleryState> emit,
  ) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Video selected',
        'ID: ${event.video.id}',
      ),
    );

    emit(
      VideoGalleryLoaded(
        allVideos: state.allVideos,
        filteredVideos: state.filteredVideos,
        currentFilter: state.currentFilter,
        selectedVideo: event.video,
        storageStats: state.storageStats,
      ),
    );
  }

  /// Handles video deselection
  void _onVideoDeselected(
    VideoDeselected event,
    Emitter<VideoGalleryState> emit,
  ) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Video deselected',
      ),
    );

    emit(
      VideoGalleryLoaded(
        allVideos: state.allVideos,
        filteredVideos: state.filteredVideos,
        currentFilter: state.currentFilter,
        storageStats: state.storageStats,
      ),
    );
  }
}
