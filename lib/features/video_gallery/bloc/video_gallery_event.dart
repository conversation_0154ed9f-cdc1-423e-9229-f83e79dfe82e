part of 'video_gallery_bloc.dart';

/// {@template video_gallery_event}
/// Base class for all video gallery events.
/// {@endtemplate}
abstract class VideoGalleryEvent extends Equatable {
  /// {@macro video_gallery_event}
  const VideoGalleryEvent();

  @override
  List<Object?> get props => [];
}

/// {@template load_videos_event}
/// Event to load videos from storage.
/// {@endtemplate}
class LoadVideos extends VideoGalleryEvent {
  /// {@macro load_videos_event}
  const LoadVideos();

  @override
  String toString() => 'LoadVideos()';
}

/// {@template refresh_videos_event}
/// Event to refresh the video list by reloading from storage.
/// {@endtemplate}
class RefreshVideos extends VideoGalleryEvent {
  /// {@macro refresh_videos_event}
  const RefreshVideos();

  @override
  String toString() => 'RefreshVideos()';
}

/// {@template delete_video_event}
/// Event to delete a specific video.
/// {@endtemplate}
class DeleteVideo extends VideoGalleryEvent {
  /// {@macro delete_video_event}
  const DeleteVideo(this.video);

  /// The video to delete
  final VideoItem video;

  @override
  List<Object> get props => [video];

  @override
  String toString() => 'DeleteVideo(video: ${video.id})';
}

/// {@template apply_filter_event}
/// Event to apply a filter to the video list.
/// {@endtemplate}
class ApplyFilter extends VideoGalleryEvent {
  /// {@macro apply_filter_event}
  const ApplyFilter(this.filter);

  /// The filter to apply
  final GalleryFilter filter;

  @override
  List<Object> get props => [filter];

  @override
  String toString() => 'ApplyFilter(filter: $filter)';
}

/// {@template clear_filter_event}
/// Event to clear all filters and show all videos.
/// {@endtemplate}
class ClearFilter extends VideoGalleryEvent {
  /// {@macro clear_filter_event}
  const ClearFilter();

  @override
  String toString() => 'ClearFilter()';
}

/// {@template generate_thumbnail_event}
/// Event to generate a thumbnail for a video.
/// {@endtemplate}
class GenerateThumbnail extends VideoGalleryEvent {
  /// {@macro generate_thumbnail_event}
  const GenerateThumbnail(this.video);

  /// The video to generate a thumbnail for
  final VideoItem video;

  @override
  List<Object> get props => [video];

  @override
  String toString() => 'GenerateThumbnail(video: ${video.id})';
}

/// {@template load_storage_stats_event}
/// Event to load storage statistics.
/// {@endtemplate}
class LoadStorageStats extends VideoGalleryEvent {
  /// {@macro load_storage_stats_event}
  const LoadStorageStats();

  @override
  String toString() => 'LoadStorageStats()';
}

/// {@template video_selected_event}
/// Event when a video is selected for viewing.
/// {@endtemplate}
class VideoSelected extends VideoGalleryEvent {
  /// {@macro video_selected_event}
  const VideoSelected(this.video);

  /// The selected video
  final VideoItem video;

  @override
  List<Object> get props => [video];

  @override
  String toString() => 'VideoSelected(video: ${video.id})';
}

/// {@template video_deselected_event}
/// Event when video selection is cleared.
/// {@endtemplate}
class VideoDeselected extends VideoGalleryEvent {
  /// {@macro video_deselected_event}
  const VideoDeselected();

  @override
  String toString() => 'VideoDeselected()';
}
