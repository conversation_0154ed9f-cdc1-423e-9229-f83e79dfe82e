part of 'video_gallery_bloc.dart';

/// {@template video_gallery_state}
/// Base class for all video gallery states.
/// {@endtemplate}
abstract class VideoGalleryState extends Equatable {
  /// {@macro video_gallery_state}
  const VideoGalleryState({
    this.allVideos = const [],
    this.filteredVideos = const [],
    this.currentFilter = const GalleryFilter(),
    this.selectedVideo,
    this.storageStats = const {},
  });

  /// All videos loaded from storage
  final List<VideoItem> allVideos;

  /// Videos after applying current filter
  final List<VideoItem> filteredVideos;

  /// Currently applied filter
  final GalleryFilter currentFilter;

  /// Currently selected video for viewing
  final VideoItem? selectedVideo;

  /// Storage statistics
  final Map<String, dynamic> storageStats;

  @override
  List<Object?> get props => [
        allVideos,
        filteredVideos,
        currentFilter,
        selectedVideo,
        storageStats,
      ];
}

/// {@template video_gallery_initial}
/// Initial state when the gallery is first loaded.
/// {@endtemplate}
class VideoGalleryInitial extends VideoGalleryState {
  /// {@macro video_gallery_initial}
  const VideoGalleryInitial();

  @override
  String toString() => 'VideoGalleryInitial()';
}

/// {@template video_gallery_loading}
/// State when videos are being loaded from storage.
/// {@endtemplate}
class VideoGalleryLoading extends VideoGalleryState {
  /// {@macro video_gallery_loading}
  const VideoGalleryLoading({
    super.allVideos,
    super.filteredVideos,
    super.currentFilter,
    super.selectedVideo,
    super.storageStats,
  });

  @override
  String toString() => 'VideoGalleryLoading()';
}

/// {@template video_gallery_loaded}
/// State when videos have been successfully loaded.
/// {@endtemplate}
class VideoGalleryLoaded extends VideoGalleryState {
  /// {@macro video_gallery_loaded}
  const VideoGalleryLoaded({
    required super.allVideos,
    required super.filteredVideos,
    super.currentFilter,
    super.selectedVideo,
    super.storageStats,
  });

  @override
  String toString() => 'VideoGalleryLoaded('
      'allVideos: ${allVideos.length}, '
      'filteredVideos: ${filteredVideos.length}, '
      'filter: $currentFilter'
      ')';
}

/// {@template video_gallery_empty}
/// State when no videos are found in storage.
/// {@endtemplate}
class VideoGalleryEmpty extends VideoGalleryState {
  /// {@macro video_gallery_empty}
  const VideoGalleryEmpty({
    super.currentFilter,
    super.storageStats,
  });

  @override
  String toString() => 'VideoGalleryEmpty()';
}

/// {@template video_gallery_error}
/// State when an error occurs while loading videos.
/// {@endtemplate}
class VideoGalleryError extends VideoGalleryState {
  /// {@macro video_gallery_error}
  const VideoGalleryError({
    required this.message,
    super.allVideos,
    super.filteredVideos,
    super.currentFilter,
    super.selectedVideo,
    super.storageStats,
  });

  /// Error message
  final String message;

  @override
  List<Object?> get props => [message, ...super.props];

  @override
  String toString() => 'VideoGalleryError(message: $message)';
}

/// {@template video_deleting}
/// State when a video is being deleted.
/// {@endtemplate}
class VideoDeleting extends VideoGalleryState {
  /// {@macro video_deleting}
  const VideoDeleting({
    required this.deletingVideo,
    required super.allVideos,
    required super.filteredVideos,
    super.currentFilter,
    super.selectedVideo,
    super.storageStats,
  });

  /// The video being deleted
  final VideoItem deletingVideo;

  @override
  List<Object?> get props => [deletingVideo, ...super.props];

  @override
  String toString() => 'VideoDeleting(video: ${deletingVideo.id})';
}

/// {@template video_deleted}
/// State when a video has been successfully deleted.
/// {@endtemplate}
class VideoDeleted extends VideoGalleryState {
  /// {@macro video_deleted}
  const VideoDeleted({
    required this.deletedVideo,
    required super.allVideos,
    required super.filteredVideos,
    super.currentFilter,
    super.selectedVideo,
    super.storageStats,
  });

  /// The video that was deleted
  final VideoItem deletedVideo;

  @override
  List<Object?> get props => [deletedVideo, ...super.props];

  @override
  String toString() => 'VideoDeleted(video: ${deletedVideo.id})';
}

/// {@template video_delete_error}
/// State when video deletion fails.
/// {@endtemplate}
class VideoDeleteError extends VideoGalleryState {
  /// {@macro video_delete_error}
  const VideoDeleteError({
    required this.video,
    required this.message,
    required super.allVideos,
    required super.filteredVideos,
    super.currentFilter,
    super.selectedVideo,
    super.storageStats,
  });

  /// The video that failed to delete
  final VideoItem video;

  /// Error message
  final String message;

  @override
  List<Object?> get props => [video, message, ...super.props];

  @override
  String toString() =>
      'VideoDeleteError(video: ${video.id}, message: $message)';
}

/// {@template thumbnail_generating}
/// State when a thumbnail is being generated.
/// {@endtemplate}
class ThumbnailGenerating extends VideoGalleryState {
  /// {@macro thumbnail_generating}
  const ThumbnailGenerating({
    required this.video,
    required super.allVideos,
    required super.filteredVideos,
    super.currentFilter,
    super.selectedVideo,
    super.storageStats,
  });

  /// The video for which thumbnail is being generated
  final VideoItem video;

  @override
  List<Object?> get props => [video, ...super.props];

  @override
  String toString() => 'ThumbnailGenerating(video: ${video.id})';
}
