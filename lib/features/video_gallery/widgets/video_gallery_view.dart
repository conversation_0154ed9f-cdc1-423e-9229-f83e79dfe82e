import 'dart:io';

import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:flutter/material.dart';

/// {@template video_thumbnail_widget}
/// Widget that displays a video thumbnail with metadata and actions.
/// {@endtemplate}
class VideoThumbnailWidget extends StatelessWidget {
  /// {@macro video_thumbnail_widget}
  const VideoThumbnailWidget({
    required this.video,
    this.onTap,
    this.onDelete,
    super.key,
  });

  /// The video to display
  final VideoItem video;

  /// Callback when the thumbnail is tapped
  final VoidCallback? onTap;

  /// Callback when delete is requested
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Thumbnail area with play overlay
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  // Thumbnail image
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.grey[300],
                    child: video.thumbnailPath != null
                        ? Image.file(
                            File(video.thumbnailPath!),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const _DefaultThumbnail();
                            },
                          )
                        : const _DefaultThumbnail(),
                  ),

                  // Play overlay
                  const Positioned.fill(
                    child: Center(
                      child: Icon(
                        Icons.play_circle_outline,
                        size: 48,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  // Duration badge
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        video.formattedDuration,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  // Delete button
                  if (onDelete != null)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () => _showDeleteConfirmation(context),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.8),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.delete,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Video metadata
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingS),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Date and time
                    Text(
                      '${video.formattedDate} ${video.formattedTime}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Quality score and file size
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Quality score with color coding
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getQualityColor(video.qualityScore)
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: _getQualityColor(video.qualityScore),
                            ),
                          ),
                          child: Text(
                            video.formattedQualityScore,
                            style: TextStyle(
                              color: _getQualityColor(video.qualityScore),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),

                        // File size
                        Text(
                          video.formattedFileSize,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shows delete confirmation dialog
  void _showDeleteConfirmation(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Video'),
          content: const Text(
            'Are you sure you want to delete this video? '
            'This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDelete?.call();
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  /// Gets color based on quality score
  Color _getQualityColor(double score) {
    if (score >= 80) return AppColors.success;
    if (score >= 70) return AppColors.warning;
    return AppColors.error;
  }
}

/// {@template default_thumbnail}
/// Default thumbnail when no thumbnail is available.
/// {@endtemplate}
class _DefaultThumbnail extends StatelessWidget {
  /// {@macro default_thumbnail}
  const _DefaultThumbnail();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.videocam_outlined,
              size: 32,
              color: Colors.grey,
            ),
            SizedBox(height: 4),
            Text(
              'No thumbnail',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// {@template empty_gallery_widget}
/// Widget displayed when the gallery is empty.
/// {@endtemplate}
class EmptyGalleryWidget extends StatelessWidget {
  /// {@macro empty_gallery_widget}
  const EmptyGalleryWidget({
    this.onCreateVideo,
    super.key,
  });

  /// Callback when user wants to create a new video
  final VoidCallback? onCreateVideo;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppDimensions.spacingL),
            Text(
              'No videos yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            Text(
              'Complete a face verification to see your videos here.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
              textAlign: TextAlign.center,
            ),
            if (onCreateVideo != null) ...[
              const SizedBox(height: AppDimensions.spacingXL),
              ElevatedButton.icon(
                onPressed: onCreateVideo,
                icon: const Icon(Icons.face),
                label: const Text('Start Face Verification'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingL,
                    vertical: AppDimensions.paddingM,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// {@template gallery_loading_widget}
/// Widget displayed while loading videos.
/// {@endtemplate}
class GalleryLoadingWidget extends StatelessWidget {
  /// {@macro gallery_loading_widget}
  const GalleryLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: AppDimensions.spacingM),
          Text('Loading videos...'),
        ],
      ),
    );
  }
}

/// {@template gallery_error_widget}
/// Widget displayed when an error occurs.
/// {@endtemplate}
class GalleryErrorWidget extends StatelessWidget {
  /// {@macro gallery_error_widget}
  const GalleryErrorWidget({
    required this.message,
    this.onRetry,
    super.key,
  });

  /// Error message to display
  final String message;

  /// Callback when retry is requested
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: AppDimensions.spacingL),
            Text(
              'Error loading videos',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w500,
                  ),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: AppDimensions.spacingXL),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
