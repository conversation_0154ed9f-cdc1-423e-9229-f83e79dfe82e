import 'dart:io';

import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

/// {@template video_gallery_service}
/// Service for managing the video gallery operations.
/// Handles video file discovery, thumbnail generation, and file management.
/// {@endtemplate}
class VideoGalleryService {
  /// {@macro video_gallery_service}
  VideoGalleryService();

  final LoggerService _logger = LoggerService();

  /// Gets all saved face verification videos
  Future<List<VideoItem>> getVideos() async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Loading videos from gallery',
        ),
      );

      final directory = await _getStorageDirectory();
      if (!await directory.exists()) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Storage directory does not exist',
            'Path: ${directory.path}',
          ),
        );
        return [];
      }

      final files = await directory
          .list()
          .where(
            (FileSystemEntity entity) =>
                entity is File && entity.path.endsWith('.mp4'),
          )
          .cast<File>()
          .toList();

      final videos = <VideoItem>[];
      for (final file in files) {
        try {
          final videoItem = await _createVideoItemFromFile(file);
          if (videoItem != null) {
            videos.add(videoItem);
          }
        } catch (error) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.videoGalleryModule,
              'Failed to process video file',
              'File: ${file.path}, Error: $error',
            ),
          );
        }
      }

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Videos loaded successfully',
          'Count: ${videos.length}',
        ),
      );

      return videos;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to load videos: $error',
        ),
        error,
        stackTrace,
      );
      return [];
    }
  }

  /// Filters and sorts videos based on the provided filter
  List<VideoItem> filterVideos(List<VideoItem> videos, GalleryFilter filter) {
    var filteredVideos = videos.toList();

    // Apply quality filter
    if (filter.minQualityScore != null) {
      filteredVideos = filteredVideos
          .where((video) => video.qualityScore >= filter.minQualityScore!)
          .toList();
    }

    // Apply date range filter
    if (filter.dateRange != null) {
      filteredVideos = filteredVideos
          .where((video) => filter.dateRange!.contains(video.createdAt))
          .toList();
    }

    // Apply sorting
    switch (filter.sortBy) {
      case GallerySortBy.dateDescending:
        filteredVideos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      case GallerySortBy.dateAscending:
        filteredVideos.sort((a, b) => a.createdAt.compareTo(b.createdAt));
      case GallerySortBy.qualityDescending:
        filteredVideos.sort((a, b) => b.qualityScore.compareTo(a.qualityScore));
      case GallerySortBy.qualityAscending:
        filteredVideos.sort((a, b) => a.qualityScore.compareTo(b.qualityScore));
      case GallerySortBy.sizeDescending:
        filteredVideos.sort((a, b) => b.fileSize.compareTo(a.fileSize));
      case GallerySortBy.sizeAscending:
        filteredVideos.sort((a, b) => a.fileSize.compareTo(b.fileSize));
    }

    return filteredVideos;
  }

  /// Deletes a video and its thumbnail
  Future<bool> deleteVideo(VideoItem video) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Deleting video',
          'ID: ${video.id}',
        ),
      );

      // Delete video file
      final videoFile = File(video.filePath);
      if (await videoFile.exists()) {
        await videoFile.delete();
      }

      // Delete thumbnail if it exists
      if (video.thumbnailPath != null) {
        final thumbnailFile = File(video.thumbnailPath!);
        if (await thumbnailFile.exists()) {
          await thumbnailFile.delete();
        }
      }

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video deleted successfully',
          'ID: ${video.id}',
        ),
      );

      return true;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to delete video: $error',
          'ID: ${video.id}',
        ),
        error,
        stackTrace,
      );
      return false;
    }
  }

  /// Generates a thumbnail for a video
  Future<String?> generateThumbnail(VideoItem video) async {
    try {
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: video.filePath,
        thumbnailPath: await _getThumbnailDirectory(),
        imageFormat: ImageFormat.JPEG,
        maxHeight: 200,
        quality: 75,
      );

      if (thumbnailPath != null) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Thumbnail generated',
            'Video: ${video.id}, Thumbnail: $thumbnailPath',
          ),
        );
      }

      return thumbnailPath;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to generate thumbnail: $error',
          'Video: ${video.id}',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Gets storage statistics
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final videos = await getVideos();
      final totalSize =
          videos.fold<int>(0, (sum, video) => sum + video.fileSize);
      final averageQuality = videos.isEmpty
          ? 0.0
          : videos.fold<double>(0, (sum, video) => sum + video.qualityScore) /
              videos.length;

      return {
        'totalVideos': videos.length,
        'totalSize': totalSize,
        'averageQuality': averageQuality,
        'oldestVideo': videos.isEmpty
            ? null
            : videos
                .reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b)
                .createdAt,
        'newestVideo': videos.isEmpty
            ? null
            : videos
                .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b)
                .createdAt,
      };
    } catch (error) {
      return {
        'totalVideos': 0,
        'totalSize': 0,
        'averageQuality': 0.0,
        'oldestVideo': null,
        'newestVideo': null,
      };
    }
  }

  /// Creates a VideoItem from a file
  Future<VideoItem?> _createVideoItemFromFile(File file) async {
    try {
      // Parse filename to extract metadata
      final fileName = file.path.split('/').last;
      final createdAt = await _parseCreationDateFromFileName(fileName, file);

      // For now, use default values for duration and quality
      // These could be stored in a metadata file or extracted from video
      const duration = Duration(seconds: 9); // Default recording duration
      const qualityScore = 75.0; // Default quality score

      // Check for existing thumbnail
      final thumbnailPath = await _findExistingThumbnail(fileName);

      return VideoItem.fromFile(
        filePath: file.path,
        createdAt: createdAt,
        duration: duration,
        qualityScore: qualityScore,
        thumbnailPath: thumbnailPath,
      );
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Failed to create VideoItem from file',
          'File: ${file.path}, Error: $error',
        ),
      );
      return null;
    }
  }

  /// Parses creation date from filename or falls back to file stats
  Future<DateTime> _parseCreationDateFromFileName(
    String fileName,
    File file,
  ) async {
    try {
      // Try to parse timestamp from filename (face_verification_TIMESTAMP.mp4)
      final regex = RegExp(r'face_verification_(\d+)\.mp4');
      final match = regex.firstMatch(fileName);

      if (match != null) {
        final timestamp = int.parse(match.group(1)!);
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
    } catch (error) {
      // Ignore parsing errors and fall back to file stats
    }

    // Fall back to file creation time
    final stat = await file.stat();
    return stat.modified;
  }

  /// Finds existing thumbnail for a video file
  Future<String?> _findExistingThumbnail(String videoFileName) async {
    try {
      final thumbnailDir = await _getThumbnailDirectory();
      final thumbnailName = videoFileName.replaceAll('.mp4', '.jpg');
      final thumbnailFile = File('$thumbnailDir/$thumbnailName');

      if (await thumbnailFile.exists()) {
        return thumbnailFile.path;
      }
    } catch (error) {
      // Ignore errors and return null
    }
    return null;
  }

  /// Gets the storage directory for face verification videos
  Future<Directory> _getStorageDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory('${appDir.path}/face_verification_videos');
  }

  /// Gets the thumbnail directory
  Future<String> _getThumbnailDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final thumbnailDir =
        Directory('${appDir.path}/face_verification_thumbnails');
    if (!await thumbnailDir.exists()) {
      await thumbnailDir.create(recursive: true);
    }
    return thumbnailDir.path;
  }
}
