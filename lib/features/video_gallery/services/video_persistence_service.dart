import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:path_provider/path_provider.dart';

/// {@template video_persistence_service}
/// Service for persisting successful face verification videos.
/// <PERSON>les copying temporary videos to permanent storage with proper naming.
/// {@endtemplate}
class VideoPersistenceService {
  /// {@macro video_persistence_service}
  VideoPersistenceService();

  final LoggerService _logger = LoggerService();

  /// Saves a successful face verification video to permanent storage
  Future<String?> saveSuccessfulVideo({
    required String temporaryVideoPath,
    required FaceCoverageStats coverageStats,
    required DateTime recordingTime,
  }) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Saving successful video',
          'Temp path: $temporaryVideoPath, Quality: '
              '${coverageStats.qualityScore.toStringAsFixed(1)}%',
        ),
      );

      // Check if the temporary video meets quality threshold
      if (!coverageStats.meetsQualityThreshold) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video does not meet quality threshold, not saving',
            'Quality: '
                '${coverageStats.qualityScore.toStringAsFixed(1)}%',
          ),
        );
        return null;
      }

      // Ensure the temporary video exists
      final tempFile = File(temporaryVideoPath);
      if (!await tempFile.exists()) {
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.videoGalleryModule,
            LoggingConstants.recoverableError,
            'Temporary video file not found',
            'Path: $temporaryVideoPath',
          ),
        );
        return null;
      }

      // Generate permanent file path with readable name
      final permanentPath = await _generatePermanentVideoPath(recordingTime);

      // Copy the temporary file to permanent storage
      final permanentFile = await tempFile.copy(permanentPath);

      // Verify the copy was successful
      if (await permanentFile.exists()) {
        final fileSize = await permanentFile.length();

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video saved successfully',
            'Path: $permanentPath, Size: ${fileSize}B',
          ),
        );

        return permanentPath;
      } else {
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.videoGalleryModule,
            LoggingConstants.recoverableError,
            'Failed to copy video to permanent storage',
            'Target path: $permanentPath',
          ),
        );
        return null;
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to save successful video: $error',
          'Temp path: $temporaryVideoPath',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Cleans up temporary video files that are no longer needed
  Future<void> cleanupTemporaryVideo(String temporaryVideoPath) async {
    try {
      final tempFile = File(temporaryVideoPath);
      if (await tempFile.exists()) {
        await tempFile.delete();

        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Temporary video cleaned up',
            'Path: $temporaryVideoPath',
          ),
        );
      }
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Failed to cleanup temporary video',
          'Path: $temporaryVideoPath, Error: $error',
        ),
      );
      // Don't rethrow - cleanup failures are not critical
    }
  }

  /// Generates a permanent file path with readable naming
  Future<String> _generatePermanentVideoPath(DateTime recordingTime) async {
    final directory = await _getStorageDirectory();

    // Format: face_verification_YYYY-MM-DD_HH-MM-SS.mp4
    final formattedDate = recordingTime
        .toIso8601String()
        .substring(0, 19) // Remove milliseconds and timezone
        .replaceAll(
          ':',
          '-',
        ) // Replace colons with dashes for filename compatibility
        .replaceAll('T', '_'); // Replace T with underscore

    return '${directory.path}/face_verification_$formattedDate.mp4';
  }

  /// Gets the storage directory for face verification videos
  Future<Directory> _getStorageDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final directory = Directory('${appDir.path}/face_verification_videos');

    // Ensure directory exists
    if (!await directory.exists()) {
      await directory.create(recursive: true);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Storage directory created',
          'Path: ${directory.path}',
        ),
      );
    }

    return directory;
  }

  /// Gets storage statistics for saved videos
  Future<Map<String, dynamic>> getStorageInfo() async {
    try {
      final directory = await _getStorageDirectory();

      if (!await directory.exists()) {
        return {
          'totalVideos': 0,
          'totalSize': 0,
          'directoryExists': false,
        };
      }

      final files = await directory
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.mp4'))
          .cast<File>()
          .toList();

      var totalSize = 0;
      for (final file in files) {
        try {
          totalSize += await file.length();
        } catch (error) {
          // Skip files that can't be read
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.videoGalleryModule,
              'Could not read file size',
              'File: ${file.path}, Error: $error',
            ),
          );
        }
      }

      return {
        'totalVideos': files.length,
        'totalSize': totalSize,
        'directoryExists': true,
        'directoryPath': directory.path,
      };
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to get storage info: $error',
        ),
        error,
        stackTrace,
      );

      return {
        'totalVideos': 0,
        'totalSize': 0,
        'directoryExists': false,
        'error': error.toString(),
      };
    }
  }

  /// Checks if a video file is a valid face verification video
  bool isValidFaceVerificationVideo(String filePath) {
    final fileName = filePath.split('/').last;

    // Check if filename matches our pattern:
    // face_verification_YYYY-MM-DD_HH-MM-SS.mp4
    final regex = RegExp(
      r'^face_verification_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.mp4$',
    );
    return regex.hasMatch(fileName);
  }

  /// Extracts recording date from filename
  DateTime? extractRecordingDateFromFilename(String filePath) {
    try {
      final fileName = filePath.split('/').last;

      // Extract date part: face_verification_YYYY-MM-DD_HH-MM-SS.mp4
      final regex = RegExp(
        r'face_verification_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})\.mp4',
      );
      final match = regex.firstMatch(fileName);

      if (match != null) {
        final dateString =
            match.group(1)!.replaceAll('_', 'T'); // Convert back to ISO format

        // Convert back time part colons (only the last two dashes)
        final parts = dateString.split('T');
        if (parts.length == 2) {
          final datePart = parts[0];
          final timePart = parts[1].replaceAll('-', ':');
          final fullDateString = '${datePart}T$timePart';
          return DateTime.parse('${fullDateString}Z'); // Add Z for UTC
        }
      }
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Failed to extract date from filename',
          'File: $filePath, Error: $error',
        ),
      );
    }

    return null;
  }
}
