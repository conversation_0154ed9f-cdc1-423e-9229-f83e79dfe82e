import 'package:equatable/equatable.dart';

/// {@template gallery_filter}
/// Filter options for the video gallery.
/// {@endtemplate}
class GalleryFilter extends Equatable {
  /// {@macro gallery_filter}
  const GalleryFilter({
    this.sortBy = GallerySortBy.dateDescending,
    this.minQualityScore,
    this.dateRange,
  });

  /// How to sort the videos
  final GallerySortBy sortBy;

  /// Minimum quality score filter (0-100)
  final double? minQualityScore;

  /// Date range filter
  final DateRange? dateRange;

  /// Creates a copy with updated fields
  GalleryFilter copyWith({
    GallerySortBy? sortBy,
    double? minQualityScore,
    DateRange? dateRange,
  }) {
    return GalleryFilter(
      sortBy: sortBy ?? this.sortBy,
      minQualityScore: minQualityScore ?? this.minQualityScore,
      dateRange: dateRange ?? this.dateRange,
    );
  }

  /// Clears all filters
  GalleryFilter clear() {
    return const GalleryFilter();
  }

  /// Whether any filters are active
  bool get hasActiveFilters {
    return minQualityScore != null || dateRange != null;
  }

  @override
  List<Object?> get props => [sortBy, minQualityScore, dateRange];

  @override
  String toString() => 'GalleryFilter('
      'sortBy: $sortBy, '
      'minQuality: $minQualityScore, '
      'dateRange: $dateRange'
      ')';
}

/// {@template gallery_sort_by}
/// Sorting options for the video gallery.
/// {@endtemplate}
enum GallerySortBy {
  /// Sort by creation date, newest first
  dateDescending,

  /// Sort by creation date, oldest first
  dateAscending,

  /// Sort by quality score, highest first
  qualityDescending,

  /// Sort by quality score, lowest first
  qualityAscending,

  /// Sort by file size, largest first
  sizeDescending,

  /// Sort by file size, smallest first
  sizeAscending,
}

/// Extension for GallerySortBy display names
extension GallerySortByExtension on GallerySortBy {
  /// Human-readable display name
  String get displayName {
    switch (this) {
      case GallerySortBy.dateDescending:
        return 'Newest First';
      case GallerySortBy.dateAscending:
        return 'Oldest First';
      case GallerySortBy.qualityDescending:
        return 'Best Quality';
      case GallerySortBy.qualityAscending:
        return 'Lowest Quality';
      case GallerySortBy.sizeDescending:
        return 'Largest Files';
      case GallerySortBy.sizeAscending:
        return 'Smallest Files';
    }
  }

  /// Icon for the sort option
  String get icon {
    switch (this) {
      case GallerySortBy.dateDescending:
      case GallerySortBy.dateAscending:
        return '📅';
      case GallerySortBy.qualityDescending:
      case GallerySortBy.qualityAscending:
        return '⭐';
      case GallerySortBy.sizeDescending:
      case GallerySortBy.sizeAscending:
        return '📁';
    }
  }
}

/// {@template date_range}
/// Date range filter for videos.
/// {@endtemplate}
class DateRange extends Equatable {
  /// {@macro date_range}
  const DateRange({
    required this.start,
    required this.end,
  });

  /// Start date (inclusive)
  final DateTime start;

  /// End date (inclusive)
  final DateTime end;

  /// Whether the range contains the given date
  bool contains(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final startOnly = DateTime(start.year, start.month, start.day);
    final endOnly = DateTime(end.year, end.month, end.day);

    return dateOnly.isAtSameMomentAs(startOnly) ||
        dateOnly.isAtSameMomentAs(endOnly) ||
        (dateOnly.isAfter(startOnly) && dateOnly.isBefore(endOnly));
  }

  /// Duration of the date range
  Duration get duration => end.difference(start);

  /// Human-readable description
  String get description {
    if (start.year == end.year &&
        start.month == end.month &&
        start.day == end.day) {
      return '${start.day}/${start.month}/${start.year}';
    }
    return '${start.day}/${start.month}/${start.year} - ${end.day}/${end.month}/${end.year}';
  }

  @override
  List<Object> get props => [start, end];

  @override
  String toString() => 'DateRange($description)';
}
