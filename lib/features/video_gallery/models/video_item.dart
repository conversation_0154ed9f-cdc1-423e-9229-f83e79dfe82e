import 'dart:io';

import 'package:equatable/equatable.dart';

/// {@template video_item}
/// Represents a saved face verification video with metadata.
/// {@endtemplate}
class VideoItem extends Equatable {
  /// {@macro video_item}
  const VideoItem({
    required this.id,
    required this.filePath,
    required this.fileName,
    required this.createdAt,
    required this.duration,
    required this.fileSize,
    required this.qualityScore,
    this.thumbnailPath,
  });

  /// Unique identifier for the video
  final String id;

  /// Full path to the video file
  final String filePath;

  /// Display name of the video file
  final String fileName;

  /// When the video was created
  final DateTime createdAt;

  /// Duration of the video in seconds
  final Duration duration;

  /// File size in bytes
  final int fileSize;

  /// Quality score from face verification (0-100)
  final double qualityScore;

  /// Path to the generated thumbnail (optional)
  final String? thumbnailPath;

  /// Whether the video file exists on disk
  Future<bool> get exists async {
    return File(filePath).exists();
  }

  /// Human-readable file size
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Human-readable duration
  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }

  /// Human-readable creation date
  String get formattedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  /// Human-readable creation time
  String get formattedTime {
    final hour = createdAt.hour.toString().padLeft(2, '0');
    final minute = createdAt.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Quality score as percentage string
  String get formattedQualityScore {
    return '${qualityScore.toStringAsFixed(1)}%';
  }

  /// Creates a copy of this video item with updated fields
  VideoItem copyWith({
    String? id,
    String? filePath,
    String? fileName,
    DateTime? createdAt,
    Duration? duration,
    int? fileSize,
    double? qualityScore,
    String? thumbnailPath,
  }) {
    return VideoItem(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      fileName: fileName ?? this.fileName,
      createdAt: createdAt ?? this.createdAt,
      duration: duration ?? this.duration,
      fileSize: fileSize ?? this.fileSize,
      qualityScore: qualityScore ?? this.qualityScore,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
    );
  }

  /// Creates a VideoItem from a file path and metadata
  static Future<VideoItem> fromFile({
    required String filePath,
    required DateTime createdAt,
    required Duration duration,
    required double qualityScore,
    String? thumbnailPath,
  }) async {
    final file = File(filePath);
    final fileName = file.path.split('/').last;
    final fileSize = await file.length();
    final id = fileName.replaceAll('.mp4', '');

    return VideoItem(
      id: id,
      filePath: filePath,
      fileName: fileName,
      createdAt: createdAt,
      duration: duration,
      fileSize: fileSize,
      qualityScore: qualityScore,
      thumbnailPath: thumbnailPath,
    );
  }

  @override
  List<Object?> get props => [
        id,
        filePath,
        fileName,
        createdAt,
        duration,
        fileSize,
        qualityScore,
        thumbnailPath,
      ];

  @override
  String toString() => 'VideoItem('
      'id: $id, '
      'fileName: $fileName, '
      'duration: $formattedDuration, '
      'size: $formattedFileSize, '
      'quality: $formattedQualityScore'
      ')';
}
