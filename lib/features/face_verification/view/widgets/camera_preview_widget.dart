import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template camera_preview_widget}
/// Widget that displays the camera preview with face detection overlay.
///
/// Shows a full-screen camera preview with a face guide overlay
/// and real-time face detection feedback.
/// {@endtemplate}
class CameraPreviewWidget extends StatefulWidget {
  /// {@macro camera_preview_widget}
  const CameraPreviewWidget({super.key});

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget> {
  final LoggerService _logger = LoggerService();
  FaceDetectionService? _faceDetectionService;
  Timer? _faceDetectionTimer;
  bool _isProcessingFrame = false;
  String? _currentVideoPath;
  int _frameCount = 0; // For frame throttling

  @override
  void initState() {
    super.initState();
    _initializeFaceDetectionService();
  }

  @override
  void dispose() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposing',
      ),
    );

    // Cancel timer first to stop processing
    _faceDetectionTimer?.cancel();
    _faceDetectionTimer = null;

    // Dispose face detection service
    _faceDetectionService?.dispose();
    _faceDetectionService = null;

    // Clear processing flag
    _isProcessingFrame = false;
    _currentVideoPath = null;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposed successfully',
      ),
    );

    super.dispose();
  }

  /// Initializes the face detection service (main thread)
  Future<void> _initializeFaceDetectionService() async {
    try {
      _faceDetectionService = FaceDetectionService();
      await _faceDetectionService!.initialize();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service initialized',
          'Main thread processing with frame throttling for optimal '
              'performance',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection service initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow; // Re-throw to prevent silent failures
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FaceVideoCaptureBloc, FaceVideoCaptureState>(
      builder: (context, state) {
        return Stack(
          children: [
            // Camera preview background
            _buildCameraPreview(context, state),

            // Face guide overlay
            FaceGuideOverlay(
              currentDetection: state.currentDetection,
              isRecording: state is Recording,
            ),
          ],
        );
      },
    );
  }

  /// Builds the camera preview with CamerAwesome integration
  Widget _buildCameraPreview(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    // Handle different states
    if (state is CameraInitializing) {
      return _buildLoadingState();
    }

    if (state is Error) {
      return _buildErrorState(state.errorMessage ?? 'Camera error occurred');
    }

    // Build CamerAwesome widget for camera ready and recording states
    return _buildCameraAwesome(context, state);
  }

  /// Builds the loading state during camera initialization
  Widget _buildLoadingState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Initializing Camera...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the error state when camera fails
  Widget _buildErrorState(String errorMessage) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                errorMessage,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<FaceVideoCaptureBloc>()
                    .add(const InitializeCamera());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the CamerAwesome widget with face detection integration
  Widget _buildCameraAwesome(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    return CameraAwesomeBuilder.awesome(
      // CRITICAL FIX: Use photo mode to prevent use case conflicts
      // This allows image analysis to work without video recording conflicts
      saveConfig: SaveConfig.photo(),
      sensorConfig: SensorConfig.single(
        sensor: Sensor.position(SensorPosition.front),
        aspectRatio: CameraAspectRatios.ratio_16_9,
      ),
      onMediaTap: _handleVideoCapture,
      onImageForAnalysis: (analysisImage) async {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame received for analysis',
            'Size: ${analysisImage.width}x${analysisImage.height}, '
                'Format: ${analysisImage.runtimeType}',
          ),
        );
        _processFrameForFaceDetection(analysisImage);
      },
      imageAnalysisConfig: AnalysisConfig(
        androidOptions: const AndroidAnalysisOptions.nv21(
          width: 480,
          // Higher resolution for better face detection
          //Autostart is always true no need to add it here
        ),
        maxFramesPerSecond: 10, // Optimized FPS for face detection
      ),
      theme: AwesomeTheme(
        bottomActionsBackgroundColor: Colors.transparent,
        buttonTheme: AwesomeButtonTheme(
          backgroundColor: Colors.transparent,
          iconSize: 0, // Hide default buttons
        ),
      ),
    );
  }

  /// Handles video capture completion
  void _handleVideoCapture(MediaCapture mediaCapture) {
    _currentVideoPath = mediaCapture.captureRequest.when(
      single: (single) => single.file?.path,
      multiple: (multiple) => null,
    );

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video capture completed',
        'Path: $_currentVideoPath',
      ),
    );
  }

  /// Processes camera frames for real-time face detection on main thread
  void _processFrameForFaceDetection(AnalysisImage analysisImage) {
    // Implement frame throttling - process every 2nd frame for better
    // responsiveness
    _frameCount++;
    if (_frameCount % 2 != 0) {
      return;
    }

    // Skip if already processing a frame, service is null, or widget disposed
    if (_isProcessingFrame || _faceDetectionService == null || !mounted) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Frame processing skipped',
          'Processing: $_isProcessingFrame, '
              'Service: ${_faceDetectionService != null}, Mounted: $mounted',
        ),
      );
      return;
    }

    _isProcessingFrame = true;
    final frameStartTime = DateTime.now();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Frame processing started',
        'Size: ${analysisImage.width}x${analysisImage.height}, '
            'Frame: $_frameCount',
      ),
    );

    // Convert AnalysisImage to InputImage for ML Kit processing
    final inputImageFuture = _convertAnalysisImageToInputImage(analysisImage);

    inputImageFuture.then((inputImage) {
      if (inputImage == null) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Failed to convert AnalysisImage to InputImage',
            'Frame processing aborted',
          ),
        );
        _isProcessingFrame = false;
        return;
      }

      // Process frame using main thread service with reduced timeout
      final processingFuture =
          _faceDetectionService!.processImage(inputImage).timeout(
        const Duration(
          milliseconds: 200,
        ), // Reduced timeout for faster response
        onTimeout: () {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Frame processing timeout on main thread',
              'Processing took longer than 200ms',
            ),
          );
          return null;
        },
      );

      processingFuture.then((result) {
        final processingTime = DateTime.now().difference(frameStartTime);

        if (result != null && mounted) {
          try {
            // Send detection result to BLoC
            context.read<FaceVideoCaptureBloc>().add(ProcessFrame(result));

            _logger.debug(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'ProcessFrame event sent to BLoC',
                'Faces: ${result.faceCount}, Coverage: '
                    '${result.coveragePercentage.toStringAsFixed(1)}%, '
                    'Time: ${processingTime.inMilliseconds}ms',
              ),
            );
          } catch (error) {
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'BLoC access failed during frame processing',
                'Error: $error',
              ),
            );
          }
        } else if (result == null) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Frame processing returned null result',
              'Time: ${processingTime.inMilliseconds}ms',
            ),
          );
        }
      }).catchError((Object error) {
        final processingTime = DateTime.now().difference(frameStartTime);
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection frame processing failed',
            'Error: $error, Time: ${processingTime.inMilliseconds}ms',
          ),
        );
      }).whenComplete(() {
        _isProcessingFrame = false;
        final totalTime = DateTime.now().difference(frameStartTime);
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame processing completed',
            'Total time: ${totalTime.inMilliseconds}ms',
          ),
        );
      });
    }).catchError((Object error) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'AnalysisImage conversion failed',
          'Error: $error',
        ),
      );
      _isProcessingFrame = false;
    });
  }

  /// Converts CamerAwesome AnalysisImage to ML Kit InputImage
  Future<InputImage?> _convertAnalysisImageToInputImage(
    AnalysisImage analysisImage,
  ) async {
    try {
      return analysisImage.when(
        nv21: (nv21Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting NV21 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}',
            ),
          );

          return InputImage.fromBytes(
            bytes: nv21Image.bytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.nv21,
              bytesPerRow: analysisImage.width,
            ),
          );
        },
        bgra8888: (bgra8888Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting BGRA8888 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}',
            ),
          );

          return InputImage.fromBytes(
            bytes: bgra8888Image.bytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.bgra8888,
              bytesPerRow: analysisImage.width * 4,
            ),
          );
        },
        yuv420: (yuv420Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting YUV420 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}',
            ),
          );

          // Combine YUV420 planes
          final combinedBytes = _combineYUV420Planes(yuv420Image);

          return InputImage.fromBytes(
            bytes: combinedBytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.yuv420,
              bytesPerRow: analysisImage.width,
            ),
          );
        },
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'AnalysisImage to InputImage conversion failed: $error',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Combines YUV420 planes into a single byte array
  Uint8List _combineYUV420Planes(Yuv420Image yuv420Image) {
    final planes = yuv420Image.planes;
    final allBytes = <int>[];

    for (final plane in planes) {
      allBytes.addAll(plane.bytes);
    }

    return Uint8List.fromList(allBytes);
  }
}
