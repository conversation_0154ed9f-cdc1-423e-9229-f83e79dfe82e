import 'package:bloomg_flutter/features/face_verification/constants/face_detection_design_tokens.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:flutter/material.dart';

/// {@template face_guide_overlay}
/// Widget that displays a face guide overlay with real-time detection feedback.
///
/// Shows an oval guide for face positioning and changes color based on
/// face detection quality and coverage.
/// {@endtemplate}
class FaceGuideOverlay extends StatefulWidget {
  /// {@macro face_guide_overlay}
  const FaceGuideOverlay({
    super.key,
    this.currentDetection,
    this.isRecording = false,
  });

  /// Current face detection result
  final FaceDetectionResult? currentDetection;

  /// Whether recording is in progress
  final bool isRecording;

  @override
  State<FaceGuideOverlay> createState() => _FaceGuideOverlayState();
}

class _FaceGuideOverlayState extends State<FaceGuideOverlay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // Color animation for smooth transitions
  late AnimationController _colorController;
  late Animation<Color?> _colorAnimation;

  Color _currentGuideColor = FaceDetectionDesignTokens.noDetectionColor;
  DetectionState _currentState = DetectionState.noFace;
  DetectionState? _previousState;

  @override
  void initState() {
    super.initState();

    // Subtle pulse animation for guide (slower and less prominent)
    _pulseController = AnimationController(
      duration: const Duration(seconds: 4), // Slower pulse
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.95, // Less dramatic scaling
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ),
    );

    // Color transition animation for smooth feedback
    _colorController = AnimationController(
      duration: const Duration(milliseconds: 500), // Smooth color transitions
      vsync: this,
    );

    _updateColorAnimation();
    _pulseController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(FaceGuideOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Get current detection state
    final newState = widget.currentDetection != null
        ? FaceDetectionDesignTokens.getDetectionState(
            faceDetected: widget.currentDetection!.faceDetected,
            faceCount: widget.currentDetection!.faceCount,
            coveragePercentage: widget.currentDetection!.coveragePercentage,
          )
        : DetectionState.noFace;

    // Update state and trigger effects if changed
    if (_currentState != newState) {
      _previousState = _currentState;
      _currentState = newState;

      // Update color animation
      _updateColorAnimation();
      _colorController.forward();

      // Update pulse speed based on detection state
      _updatePulseSpeed(newState);

      // Trigger haptic feedback for state changes
      _triggerHapticFeedback(newState);
    }
  }

  void _updateColorAnimation() {
    final targetColor = _getGuideColor(widget.currentDetection);
    _colorAnimation = ColorTween(
      begin: _currentGuideColor,
      end: targetColor,
    ).animate(
      CurvedAnimation(
        parent: _colorController,
        curve: Curves.easeInOut,
      ),
    );

    _colorAnimation.addListener(() {
      if (mounted) {
        setState(() {
          _currentGuideColor = _colorAnimation.value ?? Colors.white;
        });
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            // Dark overlay with cutout and face guide (combined)
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return CustomPaint(
                  size: Size(constraints.maxWidth, constraints.maxHeight),
                  painter: FaceGuideOverlayPainter(
                    guideColor: _currentGuideColor,
                    pulseValue: _pulseAnimation.value,
                    isRecording: widget.isRecording,
                  ),
                );
              },
            ),

            // Detection feedback (simplified)
            if (widget.currentDetection != null)
              _buildDetectionFeedback(constraints),

            // Instructions
          ],
        );
      },
    );
  }

  /// Builds detection feedback indicators
  Widget _buildDetectionFeedback(BoxConstraints constraints) {
    final detection = widget.currentDetection!;

    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: _currentGuideColor.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getFeedbackMessage(detection),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'Coverage: '
              '${detection.coveragePercentage.toStringAsFixed(0)}%',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Gets the guide color based on detection quality using unified design
  /// tokens
  Color _getGuideColor(FaceDetectionResult? detection) {
    if (detection == null) {
      return FaceDetectionDesignTokens.noDetectionColor;
    }

    final state = FaceDetectionDesignTokens.getDetectionState(
      faceDetected: detection.faceDetected,
      faceCount: detection.faceCount,
      coveragePercentage: detection.coveragePercentage,
    );

    return state.color;
  }

  /// Gets feedback message based on detection result using unified design
  /// tokens
  String _getFeedbackMessage(FaceDetectionResult detection) {
    final state = FaceDetectionDesignTokens.getDetectionState(
      faceDetected: detection.faceDetected,
      faceCount: detection.faceCount,
      coveragePercentage: detection.coveragePercentage,
    );

    return FaceDetectionDesignTokens.getFeedbackMessage(state);
  }

  /// Updates pulse animation speed based on detection state
  void _updatePulseSpeed(DetectionState state) {
    final newDuration = Duration(
      milliseconds:
          (FaceDetectionDesignTokens.defaultPulseDuration.inMilliseconds /
                  state.pulseSpeed)
              .round(),
    );

    if (_pulseController.duration != newDuration) {
      _pulseController
        ..duration = newDuration
        ..reset()
        ..repeat(reverse: true);
    }
  }

  /// Triggers haptic feedback for detection state changes
  void _triggerHapticFeedback(DetectionState state) {
    // Only trigger haptic feedback if state actually changed
    if (_previousState != null && _previousState != state) {
      state.triggerHapticFeedback();
    }
  }
}

/// {@template face_guide_overlay_painter}
/// Custom painter for the face guide overlay with cutout effect.
/// {@endtemplate}
class FaceGuideOverlayPainter extends CustomPainter {
  /// {@macro face_guide_overlay_painter}
  const FaceGuideOverlayPainter({
    required this.guideColor,
    required this.pulseValue,
    required this.isRecording,
  });

  /// Color of the guide border
  final Color guideColor;

  /// Pulse animation value (0.0 to 1.0)
  final double pulseValue;

  /// Whether recording is in progress
  final bool isRecording;

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate face guide dimensions and position
    final guideWidth = size.width * 0.7;
    final guideHeight = size.height * 0.5;
    final guideRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: guideWidth * pulseValue,
      height: guideHeight * pulseValue,
    );

    // Create a path for the entire canvas
    final fullPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Create a path for the face guide oval (this will be the clear area)
    final guidePath = Path()..addOval(guideRect);

    // Create the overlay path by subtracting the guide from the full canvas
    final overlayPath = Path.combine(
      PathOperation.difference,
      fullPath,
      guidePath,
    );

    // Draw the dark overlay only outside the face guide area
    final overlayPaint = Paint()..color = Colors.black.withValues(alpha: 0.5);
    canvas.drawPath(overlayPath, overlayPaint);

    // Draw guide border with optional glow effect
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = isRecording ? 4.0 : 3.0
      ..color = guideColor;

    if (isRecording) {
      // Add subtle glow effect during recording
      borderPaint.maskFilter = const MaskFilter.blur(BlurStyle.outer, 2);
    }

    canvas.drawOval(guideRect, borderPaint);
  }

  @override
  bool shouldRepaint(covariant FaceGuideOverlayPainter oldDelegate) {
    return oldDelegate.guideColor != guideColor ||
        oldDelegate.pulseValue != pulseValue ||
        oldDelegate.isRecording != isRecording;
  }
}
