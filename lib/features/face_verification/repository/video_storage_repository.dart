import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/services/camera_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:path_provider/path_provider.dart';

/// {@template video_storage_repository}
/// Repository that manages video recording and storage operations.
/// Coordinates between the camera service and file system operations.
/// {@endtemplate}
class VideoStorageRepository {
  /// {@macro video_storage_repository}
  VideoStorageRepository({
    CameraService? cameraService,
  }) : _cameraService = cameraService ?? CameraService();

  final CameraService _cameraService;
  final LoggerService _logger = LoggerService();

  bool _isInitialized = false;
  bool _isRecording = false;
  String? _currentVideoPath;

  /// Whether the repository is initialized and ready for use
  bool get isInitialized => _isInitialized;

  /// Whether video recording is currently in progress
  bool get isRecording => _isRecording;

  /// Path to the currently recording video file
  String? get currentVideoPath => _currentVideoPath;

  /// Initializes the video storage repository and camera service
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video storage repository already initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video storage repository initialization started',
      ),
    );

    try {
      await _cameraService.initialize();
      await _ensureStorageDirectory();
      _isInitialized = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video storage repository initialization completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Video storage repository initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Starts video recording with the specified configuration
  Future<void> startRecording([VideoCaptureConfig? config]) async {
    if (!_isInitialized) {
      throw StateError('Repository not initialized');
    }

    if (_isRecording) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Recording start attempted while already recording',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video recording started',
        'Config: $config',
      ),
    );

    try {
      // Generate unique video file path
      _currentVideoPath = await _generateVideoFilePath();

      // Start recording with camera service
      await _cameraService.startRecording(
        filePath: _currentVideoPath!,
        config: config,
      );

      _isRecording = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video recording started successfully',
          'File: $_currentVideoPath',
        ),
      );
    } catch (error, stackTrace) {
      _currentVideoPath = null;
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Video recording start failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Stops video recording and returns the path to the recorded file
  Future<String> stopRecording() async {
    if (!_isInitialized) {
      throw StateError('Repository not initialized');
    }

    if (!_isRecording) {
      throw StateError('No recording in progress');
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video recording stop requested',
        'File: $_currentVideoPath',
      ),
    );

    try {
      // Stop recording with camera service
      await _cameraService.stopRecording();

      final videoPath = _currentVideoPath!;
      _isRecording = false;
      _currentVideoPath = null;

      // Verify file exists and has content
      final file = File(videoPath);
      if (!await file.exists()) {
        throw Exception('Recorded video file not found: $videoPath');
      }

      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('Recorded video file is empty: $videoPath');
      }

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video recording stopped successfully',
          'File: $videoPath, Size: ${fileSize}B',
        ),
      );

      return videoPath;
    } catch (error, stackTrace) {
      _isRecording = false;
      _currentVideoPath = null;

      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Video recording stop failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Cancels current recording without saving
  Future<void> cancelRecording() async {
    if (!_isRecording) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Recording cancellation skipped - no recording in progress',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video recording cancelled',
        'File: $_currentVideoPath',
      ),
    );

    try {
      await _cameraService.stopRecording();

      // Delete the incomplete file if it exists
      if (_currentVideoPath != null) {
        final file = File(_currentVideoPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      _isRecording = false;
      _currentVideoPath = null;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Recording cancellation error: $error',
        ),
        error,
        stackTrace,
      );
      // Reset state even if cleanup fails
      _isRecording = false;
      _currentVideoPath = null;
    }
  }

  /// Deletes a recorded video file
  Future<bool> deleteVideo(String videoPath) async {
    try {
      final file = File(videoPath);
      if (await file.exists()) {
        await file.delete();

        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Video file deleted',
            'Path: $videoPath',
          ),
        );
        return true;
      }
      return false;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Video deletion failed: $error',
          'Path: $videoPath',
        ),
        error,
        stackTrace,
      );
      return false;
    }
  }

  /// Ensures the storage directory exists
  Future<void> _ensureStorageDirectory() async {
    final directory = await _getStorageDirectory();
    if (!await directory.exists()) {
      await directory.create(recursive: true);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Storage directory created',
          'Path: ${directory.path}',
        ),
      );
    }
  }

  /// Gets the storage directory for face verification videos
  Future<Directory> _getStorageDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory('${appDir.path}/face_verification_videos');
  }

  /// Generates a unique file path for a new video recording
  Future<String> _generateVideoFilePath() async {
    final directory = await _getStorageDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${directory.path}/face_verification_$timestamp.mp4';
  }

  /// Disposes of the repository and releases resources
  Future<void> dispose() async {
    if (!_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video storage repository disposal skipped - not initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video storage repository disposal started',
      ),
    );

    try {
      // Cancel any ongoing recording
      if (_isRecording) {
        await cancelRecording();
      }

      // Dispose camera service
      await _cameraService.dispose();
      _isInitialized = false;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video storage repository disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Video storage repository disposal error: $error',
        ),
        error,
        stackTrace,
      );
      // Don't rethrow disposal errors
    }
  }
}
