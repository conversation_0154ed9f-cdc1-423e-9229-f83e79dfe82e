import 'dart:async';
import 'dart:isolate';
import 'dart:typed_data';
import 'dart:ui';

import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template face_detection_isolate_service}
/// Service that handles face detection using
///  Google ML Kit in a background isolate.
/// Provides real-time face detection without blocking the main UI thread.
/// {@endtemplate}
class FaceDetectionIsolateService {
  /// {@macro face_detection_isolate_service}
  FaceDetectionIsolateService();

  final LoggerService _logger = LoggerService();

  Isolate? _isolate;
  SendPort? _sendPort;
  ReceivePort? _receivePort;
  bool _isInitialized = false;
  final Completer<void> _initCompleter = Completer<void>();

  // Performance tracking
  int _totalFramesProcessed = 0;
  int _framesWithValidFace = 0;
  DateTime? _lastFrameProcessTime;

  /// Whether the service is initialized and ready for use
  bool get isInitialized => _isInitialized;

  /// Gets performance statistics
  Map<String, dynamic> get performanceStats => {
        'totalFramesProcessed': _totalFramesProcessed,
        'framesWithValidFace': _framesWithValidFace,
        'validFacePercentage': _totalFramesProcessed > 0
            ? (_framesWithValidFace / _totalFramesProcessed * 100).round()
            : 0,
        'lastFrameProcessTime': _lastFrameProcessTime?.toIso8601String(),
      };

  /// Initializes the face detection isolate
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection isolate already initialized',
        ),
      );
      return;
    }

    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Initializing face detection isolate',
        ),
      );

      // Create receive port for communication from isolate
      _receivePort = ReceivePort();

      // Spawn the isolate
      _isolate = await Isolate.spawn(
        _isolateEntryPoint,
        _receivePort!.sendPort,
        debugName: 'FaceDetectionIsolate',
      );

      // Wait for isolate to send back its SendPort
      final isolateData = await _receivePort!.first as Map<String, dynamic>;

      if (isolateData['type'] == 'ready') {
        _sendPort = isolateData['sendPort'] as SendPort;
        _isInitialized = true;
        _initCompleter.complete();

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection isolate initialized successfully',
          ),
        );
      } else {
        throw StateError(
          'Isolate initialization failed: ${isolateData['error']}',
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection isolate initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      await dispose();
      rethrow;
    }
  }

  /// Processes an AnalysisImage for face detection in the background isolate
  Future<FaceDetectionResult?> processImage(AnalysisImage analysisImage) async {
    if (!_isInitialized) {
      await _initCompleter.future;
    }

    if (_sendPort == null) {
      throw StateError('Face detection isolate not properly initialized');
    }

    try {
      final startTime = DateTime.now();

      // Convert AnalysisImage to serializable data
      final imageData = await _serializeAnalysisImage(analysisImage);
      if (imageData == null) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Failed to serialize AnalysisImage',
            'Type: ${analysisImage.runtimeType}',
          ),
        );
        return null;
      }

      // Create response port for this specific request
      final responsePort = ReceivePort();

      // Send processing request to isolate
      _sendPort!.send({
        'type': 'process',
        'imageData': imageData,
        'responsePort': responsePort.sendPort,
      });

      // Wait for response with timeout
      final response = await responsePort.first.timeout(
        const Duration(milliseconds: 2000),
        onTimeout: () {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Face detection isolate processing timeout',
            ),
          );
          return {'type': 'timeout'};
        },
      ) as Map<String, dynamic>;

      responsePort.close();

      final processingTime = DateTime.now().difference(startTime);
      _totalFramesProcessed++;
      _lastFrameProcessTime = DateTime.now();

      if (response['type'] == 'result') {
        final result = _deserializeFaceDetectionResult(
          response['result'] as Map<String, dynamic>,
        );

        if (result?.isValidDetection ?? false) {
          _framesWithValidFace++;
        }

        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection isolate processing completed',
            'Faces: ${result?.faceCount ?? 0}, '
                'Coverage: '
                '${result?.coveragePercentage.toStringAsFixed(1) ?? 0}%, '
                'Time: ${processingTime.inMilliseconds}ms',
          ),
        );

        return result;
      } else if (response['type'] == 'error') {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection isolate processing error',
            'Error: ${response['error']}',
          ),
        );
        return null;
      } else {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection isolate processing timeout or unknown response',
            'Response type: ${response['type']}',
          ),
        );
        return null;
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection isolate processing failed: $error',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Disposes the isolate and cleans up resources
  Future<void> dispose() async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Disposing face detection isolate',
        'Stats: $_totalFramesProcessed frames processed, '
            '$_framesWithValidFace with valid faces',
      ),
    );

    if (_sendPort != null) {
      _sendPort!.send({'type': 'dispose'});
    }

    _isolate?.kill(priority: Isolate.immediate);
    _receivePort?.close();

    _isolate = null;
    _sendPort = null;
    _receivePort = null;
    _isInitialized = false;
  }

  /// Entry point for the face detection isolate
  static void _isolateEntryPoint(SendPort mainSendPort) {
    final receivePort = ReceivePort();
    FaceDetector? faceDetector;

    // Send the isolate's SendPort back to main thread
    mainSendPort.send({
      'type': 'ready',
      'sendPort': receivePort.sendPort,
    });

    // Initialize face detector in isolate
    try {
      final options = FaceDetectorOptions();
      faceDetector = FaceDetector(options: options);
    } catch (error) {
      mainSendPort.send({
        'type': 'error',
        'error': 'Failed to initialize face detector: $error',
      });
      return;
    }

    // Listen for processing requests
    receivePort.listen((message) async {
      final data = message as Map<String, dynamic>;

      if (data['type'] == 'process') {
        await _processInIsolate(
          faceDetector!,
          data['imageData'] as Map<String, dynamic>,
          data['responsePort'] as SendPort,
        );
      } else if (data['type'] == 'dispose') {
        await faceDetector?.close();
        receivePort.close();
      }
    });
  }

  /// Processes face detection in the isolate
  static Future<void> _processInIsolate(
    FaceDetector faceDetector,
    Map<String, dynamic> imageData,
    SendPort responsePort,
  ) async {
    try {
      // Reconstruct InputImage from serialized data
      final inputImage = InputImage.fromBytes(
        bytes: imageData['bytes'] as Uint8List,
        metadata: InputImageMetadata(
          size: Size(
            imageData['width'] as double,
            imageData['height'] as double,
          ),
          rotation: InputImageRotation.rotation0deg,
          format: _parseImageFormat(imageData['format'] as String),
          bytesPerRow: imageData['bytesPerRow'] as int,
        ),
      );

      // Perform face detection
      final faces = await faceDetector.processImage(inputImage);

      // Serialize result
      final result = _serializeFaceDetectionResult(faces, imageData);

      responsePort.send({
        'type': 'result',
        'result': result,
      });
    } catch (error) {
      responsePort.send({
        'type': 'error',
        'error': error.toString(),
      });
    }
  }

  /// Serializes AnalysisImage to transferable data
  Future<Map<String, dynamic>?> _serializeAnalysisImage(
    AnalysisImage analysisImage,
  ) async {
    try {
      return analysisImage.when(
        nv21: (nv21Image) => {
          'bytes': nv21Image.bytes,
          'width': analysisImage.width.toDouble(),
          'height': analysisImage.height.toDouble(),
          'format': 'nv21',
          'bytesPerRow': analysisImage.width,
        },
        bgra8888: (bgra8888Image) => {
          'bytes': bgra8888Image.bytes,
          'width': analysisImage.width.toDouble(),
          'height': analysisImage.height.toDouble(),
          'format': 'bgra8888',
          'bytesPerRow': analysisImage.width * 4,
        },
        yuv420: (yuv420Image) {
          // Combine YUV420 planes
          final combinedBytes = _combineYUV420Planes(yuv420Image);
          return {
            'bytes': combinedBytes,
            'width': analysisImage.width.toDouble(),
            'height': analysisImage.height.toDouble(),
            'format': 'yuv420',
            'bytesPerRow': analysisImage.width,
          };
        },
      );
    } catch (error) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Failed to serialize AnalysisImage',
          'Error: $error',
        ),
      );
      return null;
    }
  }

  /// Deserializes face detection result from isolate
  FaceDetectionResult? _deserializeFaceDetectionResult(
    Map<String, dynamic> data,
  ) {
    try {
      return FaceDetectionResult(
        faceDetected: data['faceDetected'] as bool,
        faceCount: data['faceCount'] as int,
        coveragePercentage: data['coveragePercentage'] as double,
        timestamp: DateTime.parse(data['timestamp'] as String),
        boundingBox: data['boundingBox'] != null
            ? FaceBoundingBox(
                left: (data['boundingBox'] as Map<String, dynamic>)['left']
                    as double,
                top: (data['boundingBox'] as Map<String, dynamic>)['top']
                    as double,
                width: (data['boundingBox'] as Map<String, dynamic>)['width']
                    as double,
                height: (data['boundingBox'] as Map<String, dynamic>)['height']
                    as double,
              )
            : null,
        confidence: data['confidence'] as double?,
      );
    } catch (error) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Failed to deserialize face detection result',
          'Error: $error',
        ),
      );
      return null;
    }
  }

  /// Helper method to parse image format string
  static InputImageFormat _parseImageFormat(String format) {
    switch (format) {
      case 'nv21':
        return InputImageFormat.nv21;
      case 'bgra8888':
        return InputImageFormat.bgra8888;
      case 'yuv420':
        return InputImageFormat.yuv420;
      default:
        return InputImageFormat.nv21;
    }
  }

  /// Serializes face detection result for transfer between isolates
  static Map<String, dynamic> _serializeFaceDetectionResult(
    List<Face> faces,
    Map<String, dynamic> imageData,
  ) {
    final timestamp = DateTime.now();

    if (faces.isEmpty) {
      return {
        'faceDetected': false,
        'faceCount': 0,
        'coveragePercentage': 0.0,
        'timestamp': timestamp.toIso8601String(),
        'boundingBox': null,
        'confidence': null,
      };
    }

    final face = faces.first;
    final faceCount = faces.length;

    // Calculate face coverage percentage
    final coveragePercentage = _calculateFaceCoverageInIsolate(
      face.boundingBox,
      imageData['width'] as double,
      imageData['height'] as double,
    );

    return {
      'faceDetected': true,
      'faceCount': faceCount,
      'coveragePercentage': coveragePercentage,
      'timestamp': timestamp.toIso8601String(),
      'boundingBox': {
        'left': face.boundingBox.left,
        'top': face.boundingBox.top,
        'width': face.boundingBox.width,
        'height': face.boundingBox.height,
      },
      'confidence': 0.9, // ML Kit doesn't provide confidence
    };
  }

  /// Calculates face coverage in isolate (static method)
  static double _calculateFaceCoverageInIsolate(
    Rect faceBounds,
    double imageWidth,
    double imageHeight,
  ) {
    // Face guide configuration (matching main service)
    const faceGuideWidthRatio = 0.7; // 70% of screen width
    const faceGuideHeightRatio = 0.8; // 80% of screen height

    // Calculate face guide area (centered oval)
    final guideWidth = imageWidth * faceGuideWidthRatio;
    final guideHeight = imageHeight * faceGuideHeightRatio;
    final guideLeft = (imageWidth - guideWidth) / 2;
    final guideTop = (imageHeight - guideHeight) / 2;

    // Calculate intersection area
    final intersectionLeft =
        faceBounds.left > guideLeft ? faceBounds.left : guideLeft;
    final intersectionTop =
        faceBounds.top > guideTop ? faceBounds.top : guideTop;
    final intersectionRight = faceBounds.right < (guideLeft + guideWidth)
        ? faceBounds.right
        : (guideLeft + guideWidth);
    final intersectionBottom = faceBounds.bottom < (guideTop + guideHeight)
        ? faceBounds.bottom
        : (guideTop + guideHeight);

    if (intersectionLeft >= intersectionRight ||
        intersectionTop >= intersectionBottom) {
      return 0; // No intersection
    }

    final intersectionArea = (intersectionRight - intersectionLeft) *
        (intersectionBottom - intersectionTop);
    final guideArea = guideWidth * guideHeight;

    return (intersectionArea / guideArea) * 100.0;
  }

  /// Combines YUV420 planes into a single byte array
  static Uint8List _combineYUV420Planes(Yuv420Image yuv420Image) {
    final planes = yuv420Image.planes;
    final allBytes = <int>[];

    for (final plane in planes) {
      allBytes.addAll(plane.bytes);
    }

    return Uint8List.fromList(allBytes);
  }
}
