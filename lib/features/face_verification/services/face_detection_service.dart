import 'dart:ui' as ui;

import 'package:bloomg_flutter/features/face_verification/constants/face_detection_design_tokens.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template face_detection_service}
/// Service that handles face detection using Google ML Kit.
/// Provides real-time face detection and coverage analysis.
/// {@endtemplate}
class FaceDetectionService {
  /// {@macro face_detection_service}
  FaceDetectionService();

  final LoggerService _logger = LoggerService();

  FaceDetector? _faceDetector;
  bool _isInitialized = false;

  // Face guide configuration (oval area in center of screen)
  static const double _faceGuideWidthRatio = 0.7; // 70% of screen width
  static const double _faceGuideHeightRatio = 0.8; // 80% of screen height
  static const double _minimumCoverageThreshold =
      70; // 70% coverage required (aligned with BLoC)

  // Face quality thresholds
  static const double _maxHeadRotationAngle = 15; // degrees
  static const double _minFaceSizeRatio = 0.1; // 10% of screen
  static const double _maxFaceSizeRatio = 0.8; // 80% of screen
  static const double _minEyeOpenProbability = 0.5; // 50% confidence
  static const int _maxFrameProcessingMs = 100; // 100ms max processing time

  // Frame processing statistics
  int _totalFramesProcessed = 0;
  int _framesWithValidFace = 0;
  DateTime? _lastFrameProcessTime;

  /// Whether the service is initialized and ready for use
  bool get isInitialized => _isInitialized;

  /// Initializes the face detection service and ML Kit detector
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service already initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service initialization started',
      ),
    );

    try {
      // Configure face detector options for optimal real-time performance
      // Use default options which should provide good performance
      final options = FaceDetectorOptions();

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Creating FaceDetector with options',
          'Using default FaceDetectorOptions for optimal performance',
        ),
      );

      _faceDetector = FaceDetector(options: options);
      _isInitialized = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service initialization completed',
          'FaceDetector created successfully with default options',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection service initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Processes an InputImage and detects faces using ML Kit
  ///
  /// This method performs real face detection on the provided InputImage
  /// and returns comprehensive face analysis results.
  Future<FaceDetectionResult?> processImage(InputImage inputImage) async {
    if (!_isInitialized) {
      throw StateError('Face detection service not initialized');
    }

    final startTime = DateTime.now();

    try {
      // Enhanced InputImage validation and debugging
      final metadata = inputImage.metadata;
      if (metadata == null) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'InputImage validation failed',
            'Metadata is null',
          ),
        );
        return null;
      }

      final size = metadata.size;
      final format = metadata.format;
      final rotation = metadata.rotation;
      final bytesPerRow = metadata.bytesPerRow;

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection processing started',
          'Size: ${size.width}x${size.height}, Format: $format, '
              'Rotation: $rotation, BytesPerRow: $bytesPerRow',
        ),
      );

      // Validate InputImage parameters
      if (size.width <= 0 || size.height <= 0) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'InputImage validation failed',
            'Invalid dimensions: ${size.width}x${size.height}',
          ),
        );
        return null;
      }

      // Process image with ML Kit face detector
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Calling ML Kit processImage',
          'Starting face detection processing',
        ),
      );

      final faces = await _faceDetector!.processImage(inputImage);

      final processingTime =
          DateTime.now().difference(startTime).inMilliseconds;
      _totalFramesProcessed++;
      _lastFrameProcessTime = DateTime.now();

      // Enhanced logging for face detection results
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'ML Kit face detection raw results',
          'Faces detected: ${faces.length}, '
              'Processing time: ${processingTime}ms',
        ),
      );

      // Log individual face details if faces are detected
      if (faces.isNotEmpty) {
        for (var i = 0; i < faces.length; i++) {
          final face = faces[i];
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Face $i details',
              'BoundingBox: ${face.boundingBox}, '
                  'HeadEulerAngleY: ${face.headEulerAngleY}, '
                  'HeadEulerAngleZ: ${face.headEulerAngleZ}',
            ),
          );
        }
      }

      // Log performance metrics
      if (processingTime > _maxFrameProcessingMs) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection processing slow',
            'Time: ${processingTime}ms, Threshold: ${_maxFrameProcessingMs}ms',
          ),
        );
      }

      // Convert ML Kit results to our format
      final result = _convertFacesToResult(faces, inputImage);

      if (result.isValidDetection) {
        _framesWithValidFace++;
      }

      // Enhanced statistics logging
      final currentValidRate = _totalFramesProcessed > 0
          ? (_framesWithValidFace / _totalFramesProcessed) * 100
          : 0.0;

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection completed',
          'Faces: ${faces.length}, Coverage: '
              '${result.coveragePercentage.toStringAsFixed(1)}%, '
              'Valid: ${result.isValidDetection}, '
              'Time: ${processingTime}ms, '
              'Stats: $_framesWithValidFace/$_totalFramesProcessed '
              '(${currentValidRate.toStringAsFixed(1)}%)',
        ),
      );

      return result;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection processing failed: $error',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Converts ML Kit Face detection results to FaceDetectionResult format
  FaceDetectionResult _convertFacesToResult(
    List<Face> faces,
    InputImage inputImage,
  ) {
    final timestamp = DateTime.now();
    final imageSize = inputImage.metadata?.size;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Converting ML Kit results to FaceDetectionResult',
        'Faces: ${faces.length}, ImageSize: $imageSize',
      ),
    );

    if (faces.isEmpty) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'No faces detected',
          'Returning empty result',
        ),
      );
      return FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: timestamp,
      );
    }

    final faceCount = faces.length;
    final primaryFace = faces.first;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Processing primary face',
        'BoundingBox: ${primaryFace.boundingBox}',
      ),
    );

    // Analyze face quality
    final faceQuality = _analyzeFaceQuality(primaryFace, imageSize);

    // Calculate face coverage within guide area
    double coveragePercentage = 0;
    if (imageSize != null) {
      coveragePercentage = _calculateRealFaceCoverage(
        primaryFace.boundingBox,
        Size(imageSize.width, imageSize.height),
      );

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face coverage calculated',
          'Coverage: ${coveragePercentage.toStringAsFixed(1)}%',
        ),
      );
    } else {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Cannot calculate coverage - no image size',
          'ImageSize is null',
        ),
      );
    }

    // Convert ML Kit bounding box to our format
    final boundingBox = FaceBoundingBox(
      left: primaryFace.boundingBox.left,
      top: primaryFace.boundingBox.top,
      width: primaryFace.boundingBox.width,
      height: primaryFace.boundingBox.height,
    );

    final result = FaceDetectionResult(
      faceDetected: true,
      faceCount: faceCount,
      coveragePercentage: coveragePercentage,
      timestamp: timestamp,
      boundingBox: boundingBox,
      confidence: faceQuality.overallConfidence,
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'FaceDetectionResult created',
        'Detected: ${result.faceDetected}, Count: ${result.faceCount}, '
            'Coverage: ${result.coveragePercentage.toStringAsFixed(1)}%, '
            'Confidence: ${result.confidence?.toStringAsFixed(2)}',
      ),
    );

    return result;
  }

  /// Analyzes the quality of a detected face
  FaceQuality _analyzeFaceQuality(Face face, ui.Size? imageSize) {
    var overallConfidence = 0.8; // Base confidence for ML Kit detection
    final issues = <String>[];

    // Check head rotation angles
    final headEulerAngleX = face.headEulerAngleX ?? 0;
    final headEulerAngleY = face.headEulerAngleY ?? 0;
    final headEulerAngleZ = face.headEulerAngleZ ?? 0;

    if (headEulerAngleX.abs() > _maxHeadRotationAngle ||
        headEulerAngleY.abs() > _maxHeadRotationAngle ||
        headEulerAngleZ.abs() > _maxHeadRotationAngle) {
      issues.add('Head rotation too high');
      overallConfidence -= 0.2;
    }

    // Check face size relative to image
    if (imageSize != null) {
      final faceArea = face.boundingBox.width * face.boundingBox.height;
      final imageArea = imageSize.width * imageSize.height;
      final faceSizeRatio = faceArea / imageArea;

      if (faceSizeRatio < _minFaceSizeRatio) {
        issues.add('Face too small');
        overallConfidence -= 0.3;
      } else if (faceSizeRatio > _maxFaceSizeRatio) {
        issues.add('Face too large');
        overallConfidence -= 0.2;
      }
    }

    // Check eye openness if available
    final leftEyeOpenProbability = face.leftEyeOpenProbability;
    final rightEyeOpenProbability = face.rightEyeOpenProbability;

    if (leftEyeOpenProbability != null && rightEyeOpenProbability != null) {
      if (leftEyeOpenProbability < _minEyeOpenProbability ||
          rightEyeOpenProbability < _minEyeOpenProbability) {
        issues.add('Eyes not sufficiently open');
        overallConfidence -= 0.1;
      }
    }

    return FaceQuality(
      overallConfidence: overallConfidence.clamp(0.0, 1.0),
      issues: issues,
      headRotationX: headEulerAngleX,
      headRotationY: headEulerAngleY,
      headRotationZ: headEulerAngleZ,
      leftEyeOpenProbability: leftEyeOpenProbability,
      rightEyeOpenProbability: rightEyeOpenProbability,
    );
  }

  /// Validates if the face coverage meets the minimum threshold
  bool validateCoverage(FaceDetectionResult result) {
    return result.faceDetected &&
        result.faceCount == 1 &&
        result.coveragePercentage >= _minimumCoverageThreshold;
  }

  /// Calculates real face coverage percentage within the oval guide area
  ///
  /// Returns the percentage of the detected face that is within the oval guide.
  /// This is calculated as: (intersection area / face area) * 100
  double _calculateRealFaceCoverage(ui.Rect faceBounds, Size screenSize) {
    // Calculate face guide area (centered oval)
    final guideWidth = screenSize.width * _faceGuideWidthRatio;
    final guideHeight = screenSize.height * _faceGuideHeightRatio;
    final guideLeft = (screenSize.width - guideWidth) / 2;
    final guideTop = (screenSize.height - guideHeight) / 2;

    // For oval intersection, we approximate using ellipse area calculation
    // Convert face rectangle to center and dimensions
    final faceCenterX = faceBounds.left + (faceBounds.width / 2);
    final faceCenterY = faceBounds.top + (faceBounds.height / 2);
    final faceWidth = faceBounds.width;
    final faceHeight = faceBounds.height;

    // Calculate guide oval center and radii
    final guideCenterX = guideLeft + (guideWidth / 2);
    final guideCenterY = guideTop + (guideHeight / 2);
    final guideRadiusX = guideWidth / 2;
    final guideRadiusY = guideHeight / 2;

    // Calculate face area for proper coverage calculation
    final faceArea = faceWidth * faceHeight;

    // Validate face area to prevent division by zero
    if (faceArea <= 0) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Invalid face area detected',
          'Face area: $faceArea, returning 0% coverage',
        ),
      );
      return 0;
    }

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face coverage calculation started',
        'Face: center($faceCenterX, $faceCenterY) '
            'size(${faceWidth.toStringAsFixed(1)}, '
            '${faceHeight.toStringAsFixed(1)}) '
            'area=${faceArea.toStringAsFixed(1)}, '
            'Guide: center($guideCenterX, $guideCenterY) '
            'radii(${guideRadiusX.toStringAsFixed(1)}, '
            '${guideRadiusY.toStringAsFixed(1)})',
      ),
    );

    // Calculate intersection area between face rectangle and guide oval
    final intersectionArea = _calculateRectangleOvalIntersection(
      faceCenterX,
      faceCenterY,
      faceWidth,
      faceHeight,
      guideCenterX,
      guideCenterY,
      guideRadiusX,
      guideRadiusY,
    );

    // CRITICAL FIX: Calculate percentage of FACE that is within guide
    // (not guide filled by face) - This is the correct way to measure coverage
    final rawCoveragePercentage = (intersectionArea / faceArea) * 100.0;

    // Clamp coverage to valid range (0-100%)
    final coveragePercentage = rawCoveragePercentage.clamp(0.0, 100.0);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face coverage calculation completed',
        'Intersection area: ${intersectionArea.toStringAsFixed(1)}, '
            'Face area: ${faceArea.toStringAsFixed(1)}, '
            'Raw coverage: ${rawCoveragePercentage.toStringAsFixed(1)}%, '
            'Final coverage: ${coveragePercentage.toStringAsFixed(1)}%',
      ),
    );

    return coveragePercentage;
  }

  /// Calculates intersection area between a rectangle and an oval
  /// using high-resolution sampling
  double _calculateRectangleOvalIntersection(
    double rectCenterX,
    double rectCenterY,
    double rectWidth,
    double rectHeight,
    double ovalCenterX,
    double ovalCenterY,
    double ovalRadiusX,
    double ovalRadiusY,
  ) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Calculating rectangle-oval intersection',
        'Rect: center($rectCenterX, $rectCenterY) '
            'size(${rectWidth.toStringAsFixed(1)}, '
            '${rectHeight.toStringAsFixed(1)}), '
            'Oval: center($ovalCenterX, $ovalCenterY) '
            'radii(${ovalRadiusX.toStringAsFixed(1)}, '
            '${ovalRadiusY.toStringAsFixed(1)})',
      ),
    );

    // Use high-resolution sampling for accurate intersection calculation
    // 50x50 grid = 2500 sample points (25x more accurate than before)
    const samplesPerSide = 50;
    const totalSamplePoints = samplesPerSide * samplesPerSide;

    final rectLeft = rectCenterX - (rectWidth / 2);
    final rectTop = rectCenterY - (rectHeight / 2);
    final rectRight = rectCenterX + (rectWidth / 2);
    final rectBottom = rectCenterY + (rectHeight / 2);

    var pointsInside = 0;

    // Sample points uniformly across the rectangle
    for (var row = 0; row < samplesPerSide; row++) {
      for (var col = 0; col < samplesPerSide; col++) {
        // Calculate sample point coordinates with proper spacing
        final x = rectLeft + (rectWidth * col / (samplesPerSide - 1));
        final y = rectTop + (rectHeight * row / (samplesPerSide - 1));

        // Ensure point is within rectangle bounds (edge case handling)
        if (x >= rectLeft &&
            x <= rectRight &&
            y >= rectTop &&
            y <= rectBottom) {
          // Check if point is inside oval using ellipse equation
          final normalizedX = (x - ovalCenterX) / ovalRadiusX;
          final normalizedY = (y - ovalCenterY) / ovalRadiusY;

          if ((normalizedX * normalizedX) + (normalizedY * normalizedY) <= 1) {
            pointsInside++;
          }
        }
      }
    }

    final intersectionArea =
        (pointsInside / totalSamplePoints) * (rectWidth * rectHeight);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Rectangle-oval intersection calculated',
        'Points inside: $pointsInside/$totalSamplePoints, '
            'Intersection area: ${intersectionArea.toStringAsFixed(1)}, '
            'Rectangle area: ${(rectWidth * rectHeight).toStringAsFixed(1)}',
      ),
    );

    return intersectionArea;
  }

  /// Gets the current face detection configuration
  Map<String, dynamic> getConfiguration() {
    return {
      'isInitialized': _isInitialized,
      'faceGuideWidthRatio': _faceGuideWidthRatio,
      'faceGuideHeightRatio': _faceGuideHeightRatio,
      'minimumCoverageThreshold': _minimumCoverageThreshold,
      'detectorOptions': _faceDetector != null
          ? {
              'enableContours': true,
              'enableLandmarks': true,
              'enableClassification': false,
              'enableTracking': false,
              'minFaceSize': 0.1,
              'performanceMode': 'fast',
            }
          : null,
    };
  }

  /// Updates the face detection configuration
  Future<void> updateConfiguration(Map<String, dynamic> config) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection configuration update requested',
        'Config: $config',
      ),
    );

    // In a real implementation, this would update detector settings
    // For now, we just log the configuration change
  }

  /// Generates real-time feedback message based on face
  /// detection result using unified design tokens
  String generateFeedbackMessage(FaceDetectionResult result) {
    final state = FaceDetectionDesignTokens.getDetectionState(
      faceDetected: result.faceDetected,
      faceCount: result.faceCount,
      coveragePercentage: result.coveragePercentage,
    );

    return FaceDetectionDesignTokens.getFeedbackMessage(state);
  }

  /// Gets frame processing statistics
  Map<String, dynamic> getProcessingStats() {
    final validFramePercentage = _totalFramesProcessed > 0
        ? (_framesWithValidFace / _totalFramesProcessed) * 100
        : 0.0;

    return {
      'totalFramesProcessed': _totalFramesProcessed,
      'framesWithValidFace': _framesWithValidFace,
      'validFramePercentage': validFramePercentage,
      'lastProcessTime': _lastFrameProcessTime?.toIso8601String(),
      'isInitialized': _isInitialized,
      'faceDetectorAvailable': _faceDetector != null,
    };
  }

  /// Diagnostic method to test face detection service health
  Future<Map<String, dynamic>> runDiagnostics() async {
    final diagnostics = <String, dynamic>{};

    try {
      diagnostics['serviceInitialized'] = _isInitialized;
      diagnostics['faceDetectorCreated'] = _faceDetector != null;
      diagnostics['processingStats'] = getProcessingStats();
      diagnostics['configuration'] = getConfiguration();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service diagnostics',
          'Initialized: $_isInitialized, '
              'Detector: ${_faceDetector != null}, '
              'Frames processed: $_totalFramesProcessed',
        ),
      );

      diagnostics['status'] = 'healthy';
    } catch (error) {
      diagnostics['status'] = 'error';
      diagnostics['error'] = error.toString();

      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service diagnostics failed',
          'Error: $error',
        ),
      );
    }

    return diagnostics;
  }

  /// Resets frame processing statistics
  void resetStats() {
    _totalFramesProcessed = 0;
    _framesWithValidFace = 0;
    _lastFrameProcessTime = null;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection statistics reset',
      ),
    );
  }

  /// Validates recording quality based on frame statistics
  bool validateRecordingQuality({double minimumValidFramePercentage = 80.0}) {
    final stats = getProcessingStats();
    final validFramePercentage = stats['validFramePercentage'] as double;

    final isValid = validFramePercentage >= minimumValidFramePercentage;

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording quality validation',
        'Valid frames: ${validFramePercentage.toStringAsFixed(1)}%, '
            'Required: $minimumValidFramePercentage%, '
            'Result: ${isValid ? 'PASS' : 'FAIL'}',
      ),
    );

    return isValid;
  }

  /// Disposes of the face detection service and releases resources
  Future<void> dispose() async {
    if (!_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal skipped - not initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service disposal started',
      ),
    );

    try {
      await _faceDetector?.close();
      _faceDetector = null;
      _isInitialized = false;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection service disposal error: $error',
        ),
        error,
        stackTrace,
      );
      // Don't rethrow disposal errors
    }
  }
}

/// {@template size}
/// Simple size class for width and height dimensions.
/// {@endtemplate}
class Size {
  /// {@macro size}
  const Size(this.width, this.height);

  /// Width dimension
  final double width;

  /// Height dimension
  final double height;

  @override
  String toString() => 'Size($width, $height)';
}

/// {@template face_quality}
/// Represents the quality analysis of a detected face.
/// {@endtemplate}
class FaceQuality {
  /// {@macro face_quality}
  const FaceQuality({
    required this.overallConfidence,
    required this.issues,
    this.headRotationX,
    this.headRotationY,
    this.headRotationZ,
    this.leftEyeOpenProbability,
    this.rightEyeOpenProbability,
  });

  /// Overall confidence score (0.0-1.0)
  final double overallConfidence;

  /// List of quality issues detected
  final List<String> issues;

  /// Head rotation angle X (up/down tilt)
  final double? headRotationX;

  /// Head rotation angle Y (left/right turn)
  final double? headRotationY;

  /// Head rotation angle Z (side tilt)
  final double? headRotationZ;

  /// Left eye open probability
  final double? leftEyeOpenProbability;

  /// Right eye open probability
  final double? rightEyeOpenProbability;

  /// Whether the face quality is acceptable
  bool get isAcceptable => overallConfidence >= 0.6 && issues.isEmpty;

  @override
  String toString() {
    return 'FaceQuality('
        'confidence: ${overallConfidence.toStringAsFixed(2)}, '
        'issues: $issues'
        ')';
  }
}
