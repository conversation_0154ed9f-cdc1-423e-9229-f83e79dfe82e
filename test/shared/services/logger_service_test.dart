import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart' hide ProductionFilter;

void main() {
  group('LoggerService', () {
    late LoggerService loggerService;

    setUp(() {
      loggerService = LoggerService();
    });

    test('should be a singleton', () {
      final instance1 = LoggerService();
      final instance2 = LoggerService();

      expect(instance1, same(instance2));
    });

    test('should provide access to logger instance', () {
      expect(loggerService.logger, isA<Logger>());
    });

    group('log level methods', () {
      test('should have trace method', () {
        expect(
          () => loggerService.trace('Test trace message'),
          returnsNormally,
        );
      });

      test('should have debug method', () {
        expect(
          () => loggerService.debug('Test debug message'),
          returnsNormally,
        );
      });

      test('should have info method', () {
        expect(() => loggerService.info('Test info message'), returnsNormally);
      });

      test('should have warning method', () {
        expect(
          () => loggerService.warning('Test warning message'),
          returnsNormally,
        );
      });

      test('should have error method', () {
        expect(
          () => loggerService.error('Test error message'),
          returnsNormally,
        );
      });

      test('should have fatal method', () {
        expect(
          () => loggerService.fatal('Test fatal message'),
          returnsNormally,
        );
      });
    });

    group('structured logging methods', () {
      test('should log user actions', () {
        expect(
          () => loggerService.logUserAction(
            'login',
            'user123',
            {'device': 'mobile', 'version': '1.0.0'},
          ),
          returnsNormally,
        );
      });

      test('should log performance metrics', () {
        expect(
          () => loggerService.logPerformance(
            'API call',
            const Duration(milliseconds: 500),
            'GET /api/users',
          ),
          returnsNormally,
        );
      });

      test('should log security events', () {
        expect(
          () => loggerService.logSecurity(
            'Failed login attempt',
            'high',
            'Multiple attempts from same IP',
          ),
          returnsNormally,
        );
      });

      test('should log authentication events', () {
        expect(
          () => loggerService.logAuth(
            'login',
            'success',
            'user123',
            'Login completed',
          ),
          returnsNormally,
        );
      });

      test('should log Firebase operations', () {
        expect(
          () => loggerService.logFirebase(
            'user_create',
            'success',
            'User created successfully',
          ),
          returnsNormally,
        );
      });

      test('should log BLoC changes', () {
        expect(
          () => loggerService.logBlocChange(
            'LoginCubit',
            'LoginInitial',
            'LoginLoading',
            'LoginSubmitted',
          ),
          returnsNormally,
        );
      });

      test('should log BLoC errors', () {
        expect(
          () => loggerService.logBlocError(
            'LoginCubit',
            Exception('Test error'),
            StackTrace.current,
          ),
          returnsNormally,
        );
      });

      test('should log navigation events', () {
        expect(
          () => loggerService.logNavigation(
            'push',
            '/login',
            {'from': '/home'},
          ),
          returnsNormally,
        );
      });

      test('should log form validation events', () {
        expect(
          () => loggerService.logFormValidation(
            'LoginForm',
            'email',
            'invalid',
            'Invalid email format',
          ),
          returnsNormally,
        );
      });
    });

    group('ProductionFilter', () {
      late ProductionFilter filter;

      setUp(() {
        filter = ProductionFilter();
      });

      test('should allow all logs in debug mode', () {
        // This test assumes we're running in debug mode
        if (kDebugMode) {
          final testEvent = LogEvent(Level.trace, 'test');
          expect(filter.shouldLog(testEvent), isTrue);

          final debugEvent = LogEvent(Level.debug, 'test');
          expect(filter.shouldLog(debugEvent), isTrue);

          final infoEvent = LogEvent(Level.info, 'test');
          expect(filter.shouldLog(infoEvent), isTrue);

          final warningEvent = LogEvent(Level.warning, 'test');
          expect(filter.shouldLog(warningEvent), isTrue);

          final errorEvent = LogEvent(Level.error, 'test');
          expect(filter.shouldLog(errorEvent), isTrue);

          final fatalEvent = LogEvent(Level.fatal, 'test');
          expect(filter.shouldLog(fatalEvent), isTrue);
        }
      });
    });

    group('environment configuration', () {
      test('should handle different log levels based on environment', () {
        // Test that the logger service can be instantiated without errors
        // The actual log level configuration is tested implicitly
        // through the logger creation
        expect(loggerService.logger, isA<Logger>());
      });
    });

    group('error handling', () {
      test('should handle null error gracefully', () {
        expect(
          () => loggerService.error('Test error'),
          returnsNormally,
        );
      });

      test('should handle null stack trace gracefully', () {
        expect(
          () => loggerService.error('Test error', Exception('test')),
          returnsNormally,
        );
      });
    });

    group('message formatting', () {
      test('should format user actions correctly', () {
        // Test the private formatting methods indirectly through public methods
        expect(
          () => loggerService.logUserAction('test_action', 'user123'),
          returnsNormally,
        );
      });

      test('should format performance messages correctly', () {
        expect(
          () => loggerService.logPerformance(
            'test_operation',
            const Duration(milliseconds: 100),
          ),
          returnsNormally,
        );
      });

      test('should format security messages correctly', () {
        expect(
          () => loggerService.logSecurity('test_event', 'medium'),
          returnsNormally,
        );
      });
    });
  });
}
