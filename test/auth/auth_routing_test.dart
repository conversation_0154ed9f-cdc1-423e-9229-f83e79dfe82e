import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/cubit/auth_cubit.dart';
import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

class MockAuthCubit extends MockCubit<AuthState> implements AuthCubit {}

void main() {
  group('Authentication Routing', () {
    late MockAuthCubit mockAuthCubit;
    late GoRouter router;

    setUp(() {
      mockAuthCubit = MockAuthCubit();

      // Setup GetIt for dependency injection
      if (GetIt.instance.isRegistered<AuthCubit>()) {
        GetIt.instance.unregister<AuthCubit>();
      }
      GetIt.instance.registerSingleton<AuthCubit>(mockAuthCubit);

      // Setup default mock behavior
      when(() => mockAuthCubit.isAuthenticated).thenReturn(false);
      when(() => mockAuthCubit.isUnauthenticated).thenReturn(true);
      when(() => mockAuthCubit.isUnknown).thenReturn(false);
      when(() => mockAuthCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([const AuthState.unauthenticated()]),
      );
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    testWidgets('should redirect authenticated user from login to home',
        (tester) async {
      // Arrange: User is authenticated
      when(() => mockAuthCubit.isAuthenticated).thenReturn(true);
      when(() => mockAuthCubit.isUnauthenticated).thenReturn(false);
      when(() => mockAuthCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          const AuthState.authenticated(
            UserModel(
              uid: 'test-uid',
              email: '<EMAIL>',
            ),
          ),
        ]),
      );

      router = AppRouter.createRouter();

      // Act: Create app with router and navigate to login
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
        ),
      );

      router.go('/login');
      await tester.pumpAndSettle();

      // Assert: Should be redirected to home
      expect(router.routerDelegate.currentConfiguration.uri.path, '/home');
    });

    testWidgets('should redirect unauthenticated user from home to login',
        (tester) async {
      // Arrange: User is not authenticated
      when(() => mockAuthCubit.isAuthenticated).thenReturn(false);
      when(() => mockAuthCubit.isUnauthenticated).thenReturn(true);

      router = AppRouter.createRouter();

      // Act: Create app with router and navigate to home
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
        ),
      );

      router.go('/home');
      await tester.pumpAndSettle();

      // Assert: Should be redirected to login
      expect(router.routerDelegate.currentConfiguration.uri.path, '/login');
    });

    testWidgets('should allow unauthenticated user to access login page',
        (tester) async {
      // Arrange: User is not authenticated
      when(() => mockAuthCubit.isAuthenticated).thenReturn(false);
      when(() => mockAuthCubit.isUnauthenticated).thenReturn(true);

      router = AppRouter.createRouter();

      // Act: Create app with router and navigate to login
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
        ),
      );

      router.go('/login');
      await tester.pumpAndSettle();

      // Assert: Should stay on login page
      expect(router.routerDelegate.currentConfiguration.uri.path, '/login');
    });

    testWidgets('should allow authenticated user to access home page',
        (tester) async {
      // Arrange: User is authenticated
      when(() => mockAuthCubit.isAuthenticated).thenReturn(true);
      when(() => mockAuthCubit.isUnauthenticated).thenReturn(false);
      when(() => mockAuthCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          const AuthState.authenticated(
            UserModel(
              uid: 'test-uid',
              email: '<EMAIL>',
            ),
          ),
        ]),
      );

      router = AppRouter.createRouter();

      // Act: Create app with router and navigate to home
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
        ),
      );

      router.go('/home');
      await tester.pumpAndSettle();

      // Assert: Should stay on home page
      expect(router.routerDelegate.currentConfiguration.uri.path, '/home');
    });

    testWidgets('should stay on current route when auth status is unknown',
        (tester) async {
      // Arrange: Auth status is unknown
      when(() => mockAuthCubit.isAuthenticated).thenReturn(false);
      when(() => mockAuthCubit.isUnauthenticated).thenReturn(false);
      when(() => mockAuthCubit.isUnknown).thenReturn(true);

      router = AppRouter.createRouter();

      // Act: Create app with router and navigate to login
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
        ),
      );

      router.go('/login');
      await tester.pumpAndSettle();

      // Assert: Should stay on login page (no redirect when unknown)
      expect(router.routerDelegate.currentConfiguration.uri.path, '/login');
    });
  });
}
