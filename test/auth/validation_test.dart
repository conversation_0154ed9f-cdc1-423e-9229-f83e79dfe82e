import 'package:bloomg_flutter/auth/models/confirmed_password.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/name.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Email Validation', () {
    test('should return empty error for empty email', () {
      const email = Email.dirty();
      expect(email.error, EmailValidationError.empty);
      expect(email.error?.message, 'Please enter your email address');
    });

    test('should return invalid error for malformed email', () {
      const email = Email.dirty('invalid-email');
      expect(email.error, EmailValidationError.invalid);
      expect(email.error?.message, 'Please enter a valid email address');
    });

    test('should be valid for correct email format', () {
      const email = Email.dirty('<EMAIL>');
      expect(email.error, isNull);
      expect(email.isValid, isTrue);
    });
  });

  group('Password Validation', () {
    test('should return empty error for empty password', () {
      const password = Password.dirty();
      expect(password.error, PasswordValidationError.empty);
      expect(password.error?.message, 'Please enter your password');
    });

    test('should return too short error for short password', () {
      const password = Password.dirty('1234567');
      expect(password.error, PasswordValidationError.tooShort);
      expect(
        password.error?.message,
        'Password must be at least 8 characters long',
      );
    });

    test('should be valid for password with 8+ characters', () {
      const password = Password.dirty('Password123');
      expect(password.error, isNull);
      expect(password.isValid, isTrue);
    });
  });

  group('Name Validation', () {
    test('should return empty error for empty name', () {
      const name = Name.dirty();
      expect(name.error, NameValidationError.empty);
      expect(name.error?.message, 'Please enter your full name');
    });

    test('should return too short error for short name', () {
      const name = Name.dirty('A');
      expect(name.error, NameValidationError.tooShort);
      expect(
        name.error?.message,
        'Name must be at least 2 characters long',
      );
    });

    test('should be valid for name with 2+ characters', () {
      const name = Name.dirty('John Doe');
      expect(name.error, isNull);
      expect(name.isValid, isTrue);
    });
  });

  group('Confirmed Password Validation', () {
    test('should return empty error for empty confirmation', () {
      const confirmedPassword =
          ConfirmedPassword.dirty(password: 'password123');
      expect(
        confirmedPassword.error,
        ConfirmedPasswordValidationError.empty,
      );
      expect(
        confirmedPassword.error?.message,
        'Please confirm your password',
      );
    });

    test('should return mismatch error for different passwords', () {
      const confirmedPassword = ConfirmedPassword.dirty(
        password: 'password123',
        value: 'different',
      );
      expect(
        confirmedPassword.error,
        ConfirmedPasswordValidationError.mismatch,
      );
      expect(confirmedPassword.error?.message, 'Passwords do not match');
    });

    test('should be valid for matching passwords', () {
      const confirmedPassword = ConfirmedPassword.dirty(
        password: 'password123',
        value: 'password123',
      );
      expect(confirmedPassword.error, isNull);
      expect(confirmedPassword.isValid, isTrue);
    });
  });
}
