import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/cubit/login_cubit.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  group('LoginCubit Lifecycle Management', () {
    late MockAuthRepository mockAuthRepository;
    late LoginCubit loginCubit;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      loginCubit = LoginCubit(mockAuthRepository);
    });

    tearDown(() {
      loginCubit.close();
    });

    group('State Emission After Close', () {
      test('should not emit states after close() is called', () async {
        // Close the cubit
        await loginCubit.close();

        // Verify cubit is closed
        expect(loginCubit.isClosed, isTrue);

        // Try to change email - should not emit any states
        loginCubit
          ..emailChanged('<EMAIL>')
          ..passwordChanged('password123')
          ..emailTouched()
          ..passwordTouched();

        // No states should be emitted after close
        // The test passes if no exceptions are thrown
      });

      test('should handle async operations gracefully after close', () async {
        // Setup mock to simulate successful login
        when(
          () => mockAuthRepository.logInWithEmailAndPassword(
            email: any(named: 'email'),
            password: any(named: 'password'),
          ),
        ).thenAnswer((_) async {
          // Simulate some delay
          await Future<void>.delayed(const Duration(milliseconds: 100));
        });

        // Set valid form data
        loginCubit
          ..emailChanged('<EMAIL>')
          ..passwordChanged('password123');

        // Start login process
        final loginFuture = loginCubit.logInWithCredentials();

        // Close cubit while login is in progress
        await loginCubit.close();

        // Wait for login to complete
        await loginFuture;

        // Verify no exceptions were thrown
        expect(loginCubit.isClosed, isTrue);
      });

      test('should handle login errors gracefully after close', () async {
        // Setup mock to simulate login failure
        when(
          () => mockAuthRepository.logInWithEmailAndPassword(
            email: any(named: 'email'),
            password: any(named: 'password'),
          ),
        ).thenThrow(Exception('Network error'));

        // Set valid form data
        loginCubit
          ..emailChanged('<EMAIL>')
          ..passwordChanged('password123');

        // Start login process
        final loginFuture = loginCubit.logInWithCredentials();

        // Close cubit while login is in progress
        await loginCubit.close();

        // Wait for login to complete (should handle error gracefully)
        await loginFuture;

        // Verify no exceptions were thrown
        expect(loginCubit.isClosed, isTrue);
      });
    });

    group('Normal Operation Before Close', () {
      test('should emit states normally when not closed', () {
        expect(loginCubit.isClosed, isFalse);

        // Test email change
        loginCubit.emailChanged('<EMAIL>');
        expect(loginCubit.state.email.value, equals('<EMAIL>'));

        // Test password change
        loginCubit.passwordChanged('password123');
        expect(loginCubit.state.password.value, equals('password123'));

        // Test touched states
        loginCubit.emailTouched();
        expect(loginCubit.state.emailTouched, isTrue);

        loginCubit.passwordTouched();
        expect(loginCubit.state.passwordTouched, isTrue);
      });

      blocTest<LoginCubit, LoginState>(
        'should handle successful login flow normally',
        build: () {
          when(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: any(named: 'email'),
              password: any(named: 'password'),
            ),
          ).thenAnswer((_) async {});
          return LoginCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123!');
          await cubit.logInWithCredentials();
        },
        expect: () => [
          // Email change
          isA<LoginState>()
              .having((s) => s.email.value, 'email', '<EMAIL>'),
          // Password change
          isA<LoginState>()
              .having((s) => s.password.value, 'password', 'Password123!')
              .having((s) => s.status, 'status', FormStatus.valid),
          // Submission in progress
          isA<LoginState>().having(
            (s) => s.status,
            'status',
            FormStatus.submissionInProgress,
          ),
          // Submission success
          isA<LoginState>()
              .having((s) => s.status, 'status', FormStatus.submissionSuccess),
        ],
      );
    });

    group('Rapid Navigation Simulation', () {
      test('should handle rapid close during form interaction', () async {
        // Simulate rapid user interaction followed by navigation
        loginCubit
          ..emailChanged('<EMAIL>')
          ..passwordChanged('password123')
          ..emailTouched();

        // Simulate immediate navigation (close)
        await loginCubit.close();

        // Try more interactions after close
        loginCubit
          ..passwordTouched()
          ..emailChanged('<EMAIL>');

        // Should not throw any exceptions
        expect(loginCubit.isClosed, isTrue);
      });
    });
  });
}
