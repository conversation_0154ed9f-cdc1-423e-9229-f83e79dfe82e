# Bitbucket Pipelines configuration for Flutter project
# This configuration provides CI/CD for the Bloomg Flutter application

image: cirrusci/flutter:stable

definitions:
  caches:
    flutter: ~/.pub-cache
  steps:
    - step: &flutter-test
        name: Flutter Test
        caches:
          - flutter
        script:
          - flutter --version
          - flutter pub get
          - flutter analyze
          - flutter test --coverage --test-randomize-ordering-seed random
        artifacts:
          - coverage/**
    - step: &flutter-build-android
        name: Build Android APK
        caches:
          - flutter
        script:
          - flutter --version
          - flutter pub get
          - flutter build apk --flavor production --target lib/main_production.dart
        artifacts:
          - build/app/outputs/flutter-apk/*.apk
    - step: &flutter-build-ios
        name: Build iOS (Development)
        caches:
          - flutter
        script:
          - flutter --version
          - flutter pub get
          - flutter build ios --flavor production --target lib/main_production.dart --no-codesign
        artifacts:
          - build/ios/iphoneos/*.app

pipelines:
  default:
    - step: *flutter-test
  
  branches:
    main:
      - step: *flutter-test
      - step: *flutter-build-android
      - step: *flutter-build-ios
    
    develop:
      - step: *flutter-test
      - step:
          name: Build Development APK
          caches:
            - flutter
          script:
            - flutter --version
            - flutter pub get
            - flutter build apk --flavor development --target lib/main_development.dart
          artifacts:
            - build/app/outputs/flutter-apk/*.apk
    
    staging:
      - step: *flutter-test
      - step:
          name: Build Staging APK
          caches:
            - flutter
          script:
            - flutter --version
            - flutter pub get
            - flutter build apk --flavor staging --target lib/main_staging.dart
          artifacts:
            - build/app/outputs/flutter-apk/*.apk

  pull-requests:
    '**':
      - step: *flutter-test

  tags:
    'v*':
      - step: *flutter-test
      - step: *flutter-build-android
      - step: *flutter-build-ios
      - step:
          name: Create Release
          script:
            - echo "Creating release for tag $BITBUCKET_TAG"
            # Add deployment scripts here if needed
