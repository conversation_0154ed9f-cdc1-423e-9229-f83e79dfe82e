# Firebase Development Documentation

## Overview
Firebase is Google's comprehensive app development platform that provides backend services, easy-to-use SDKs, and ready-made UI libraries to authenticate users, store data, and more. This documentation covers essential Firebase concepts, setup, and integration patterns.

## Getting Started

### Installation and Setup

#### Install Firebase CLI
```console
# macOS/Linux
curl -sL https://firebase.tools | bash

# Global installation via npm
npm -g install firebase-tools
```

#### Verify Installation
```console
firebase --version
```
Ensure version 9.0.0 or higher for full Emulator Suite support.

#### Login to Firebase
```console
firebase login
```
Authenticates the CLI with your Google account.

### Project Setup

#### Clone Sample Projects
```console
# Android sample
git clone https://github.com/firebase/codelab-friendlychat-android

# Web sample
git clone https://github.com/firebase/friendlyeats-web
```

#### Link Local Project to Firebase
```console
firebase use --add
```
Connects your local directory to a Firebase project.

## Flutter Integration

### Setup for Flutter

#### Configure Flutter Project
```sh
# Install FlutterFire CLI
dart pub global activate flutterfire_cli

# Configure project
flutterfire configure
```

#### Add Firebase Core
```dart
// Import required packages
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

// Initialize Firebase
await Firebase.initializeApp(
  options: DefaultFirebaseOptions.currentPlatform,
);
```

#### Add Specific Firebase Services
```sh
# Add specific plugins
flutter pub add firebase_auth
flutter pub add cloud_firestore
flutter pub add firebase_storage
flutter pub add firebase_database
```

#### Run Flutter App
```sh
flutter run
```

## Firebase Services

### Authentication

#### Phone Authentication (Flutter)

##### Start Phone Verification (Native)
```dart
await FirebaseAuth.instance.verifyPhoneNumber(
  phoneNumber: '+44 7123 123 456',
  verificationCompleted: (PhoneAuthCredential credential) async {
    // Auto-verification completed (Android only)
    await auth.signInWithCredential(credential);
  },
  verificationFailed: (FirebaseAuthException e) {
    if (e.code == 'invalid-phone-number') {
      print('The provided phone number is not valid.');
    }
  },
  codeSent: (String verificationId, int? resendToken) async {
    // SMS code sent, wait for user input
    String smsCode = 'xxxx';
    PhoneAuthCredential credential = PhoneAuthProvider.credential(
      verificationId: verificationId, 
      smsCode: smsCode
    );
    await auth.signInWithCredential(credential);
  },
  codeAutoRetrievalTimeout: (String verificationId) {
    // Auto-resolution timed out
  },
);
```

##### Web Phone Authentication
```dart
// Start verification with reCAPTCHA
ConfirmationResult confirmationResult = await auth.signInWithPhoneNumber('+44 7123 123 456');

// Confirm with SMS code
UserCredential userCredential = await confirmationResult.confirm('123456');
```

#### Connect to Auth Emulator
```dart
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // Connect to emulator for development
  await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
}
```

### Realtime Database

#### Setup and Basic Usage
```bash
# Add dependency
flutter pub add firebase_database
```

```dart
// Get database reference
DatabaseReference ref = FirebaseDatabase.instance.ref();

// For secondary app
FirebaseApp secondaryApp = Firebase.app('SecondaryApp');
FirebaseDatabase database = FirebaseDatabase.instanceFor(app: secondaryApp);
```

#### Security Rules
```json
{
  "rules": {
    "messages": {
      ".read": "auth.uid != null",
      ".write": "auth.uid != null"
    }
  }
}
```

### Cloud Storage

#### Basic Operations
```dart
// Create storage reference
final storageRef = FirebaseStorage.instance.ref();

// Create child references
Reference imagesRef = storageRef.child("images");
final fileName = "space.jpg";
final spaceRef = imagesRef.child(fileName);

// Get file properties
final path = spaceRef.fullPath;  // "images/space.jpg"
final name = spaceRef.name;      // "space.jpg"
```

#### Error Handling
```dart
try {
  final listResult = await storageRef.listAll();
} on FirebaseException catch (e) {
  print("Failed with error '${e.code}': ${e.message}");
}
```

### App Check

#### Debug Provider Setup (Flutter)

##### iOS Debug Provider
```dart
import 'package:firebase_app_check/firebase_app_check.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await FirebaseAppCheck.instance.activate(
    appleProvider: AppleProvider.debug,
  );
  runApp(App());
}
```

##### Android Debug Provider
```dart
await FirebaseAppCheck.instance.activate(
  webRecaptchaSiteKey: 'recaptcha-v3-site-key',
  androidProvider: AndroidProvider.debug,
);
```

##### Web Debug Provider
```html
<body>
  <script>
    self.FIREBASE_APPCHECK_DEBUG_TOKEN = true;
  </script>
</body>
```

## Android Integration

### Gradle Configuration

#### Project-level build.gradle
```groovy
buildscript {
    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.2'
        classpath 'com.google.gms:google-services:4.3.13'
    }
}
```

#### App-level build.gradle
```groovy
plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'com.google.gms.google-services'
}

dependencies {
    // Google Sign In SDK
    implementation 'com.google.android.gms:play-services-auth:20.2.0'
    
    // Firebase SDK
    implementation platform('com.google.firebase:firebase-bom:30.3.2')
    implementation 'com.google.firebase:firebase-database-ktx'
    implementation 'com.google.firebase:firebase-storage-ktx'
    implementation 'com.google.firebase:firebase-auth-ktx'
    
    // Firebase UI Library
    implementation 'com.firebaseui:firebase-ui-auth:8.0.1'
    implementation 'com.firebaseui:firebase-ui-database:8.0.1'
}
```

### Kotlin Implementation

#### Initialize Firebase Auth
```kotlin
// Declare instance variables
private lateinit var auth: FirebaseAuth
private lateinit var db: FirebaseDatabase

// Initialize in onCreate
auth = Firebase.auth
db = Firebase.database

// Check if user is signed in
if (auth.currentUser == null) {
    startActivity(Intent(this, SignInActivity::class.java))
    finish()
    return
}
```

#### Send Messages to Realtime Database
```kotlin
binding.sendButton.setOnClickListener { 
    val friendlyMessage = FriendlyMessage(
        binding.messageEditText.text.toString(),
        getUserName(),
        getPhotoUrl(),
        null /* no image */
    )
    db.reference.child(MESSAGES_CHILD).push().setValue(friendlyMessage)
    binding.messageEditText.setText("")
}
```

#### Sign Out Implementation
```kotlin
private fun signOut() {
    AuthUI.getInstance().signOut()
    startActivity(Intent(this, SignInActivity::class.java))
    finish()
}
```

## Development Tools

### Firebase Emulator Suite

#### Start Emulators
```shell
# Start all configured emulators
firebase emulators:start

# Start specific emulators
firebase emulators:start --only hosting
firebase emulators:start --project=demo-friendlychat-android
```

#### Connect Android App to Emulators
```kotlin
// Connect to emulators in debug mode
if (BuildConfig.DEBUG) {
    Firebase.database.useEmulator("********", 9000)
    Firebase.auth.useEmulator("********", 9099)
    Firebase.storage.useEmulator("********", 9199)
}
```

### Deployment

#### Deploy Rules and Indexes
```console
# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy Firestore indexes
firebase deploy --only firestore:indexes
```

#### Angular Deployment
```cli
ng deploy
```

## Data Structure Best Practices

### Avoid Nesting Data
```json
// ❌ Bad: Nested structure
{
  "chats": {
    "one": {
      "title": "Historical Tech Pioneers",
      "messages": {
        "m1": { "name": "eclarke", "message": "Hello" },
        "m2": { "name": "ghopper", "message": "Hi there" }
      }
    }
  }
}
```

```json
// ✅ Good: Flattened structure
{
  "chats": {
    "one": {
      "title": "Historical Tech Pioneers",
      "lastMessage": "ghopper: Relay malfunction found.",
      "timestamp": 1459361875666
    }
  },
  "members": {
    "one": {
      "ghopper": true,
      "alovelace": true,
      "eclarke": true
    }
  },
  "messages": {
    "one": {
      "m1": {
        "name": "eclarke",
        "message": "The relay seems to be malfunctioning.",
        "timestamp": 1459361875337
      }
    }
  }
}
```

## Testing and Debugging

### Crashlytics Integration

#### Force Test Crash (Flutter)
```dart
TextButton(
    onPressed: () => throw Exception(),
    child: const Text("Throw Test Exception"),
),
```

### User Management

#### Import Users via CLI
```bash
firebase auth:import users.json --hash-algo=scrypt --rounds=8 --mem-cost=14
```

## Configuration Files

### Firebase Hosting Configuration
```json
{
  "firestore": {
    "rules": "firestore.rules"
  },
  "storage": {
    "rules": "storage.rules"
  },
  "hosting": {
    "public": "./public"
  }
}
```

### Android Manifest Configuration
```xml
<!-- Disable auto data collection -->
<meta-data
    android:name="firebase_inapp_messaging_auto_data_collection_enabled"
    android:value="false" />
```

## Security

### Generate Android Debug Keystore
```console
./gradlew signingReport
```

### Environment Configuration
```typescript
export const environment = {
  firebase: {
    apiKey: "API_KEY",
    authDomain: "PROJECT_ID.firebaseapp.com",
    databaseURL: "https://PROJECT_ID.firebaseio.com",
    projectId: "PROJECT_ID",
    storageBucket: "PROJECT_ID.appspot.com",
    messagingSenderId: "SENDER_ID",
    appId: "APP_ID",
    measurementId: "G-MEASUREMENT_ID",
  },
};
```

## Best Practices

### Development Workflow
1. Use Firebase Emulator Suite for local development
2. Implement proper error handling for all Firebase operations
3. Structure data efficiently to minimize reads/writes
4. Use security rules to protect user data
5. Test authentication flows thoroughly
6. Monitor performance and costs in production

### Security Guidelines
- Always validate user input
- Use Firebase Security Rules effectively
- Implement proper authentication flows
- Regularly audit user permissions
- Use App Check for additional security

This documentation provides comprehensive coverage of Firebase integration patterns, from basic setup to advanced features and best practices for production applications.
