# CamerAwesome Integration Guide

A comprehensive guide to integrating and using the CamerAwesome plugin in your Flutter application for camera functionality, video recording, and image analysis.

## Table of Contents

1. [Overview](#overview)
2. [Installation](#installation)
3. [Platform Setup](#platform-setup)
4. [Basic Usage](#basic-usage)
5. [Built-in UI](#built-in-ui)
6. [Custom UI](#custom-ui)
7. [Camera States](#camera-states)
8. [Media Capture Events](#media-capture-events)
9. [Image Analysis](#image-analysis)
10. [Photo Filters](#photo-filters)
11. [Sensor Configuration](#sensor-configuration)
12. [Multiple Cameras](#multiple-cameras)
13. [Best Practices](#best-practices)
14. [Troubleshooting](#troubleshooting)

## Overview

CamerAwesome is a comprehensive Flutter camera plugin that provides:

- **Photo capture** with live filters
- **Video recording** with audio support
- **Image analysis** for ML/AI features (face detection, barcode scanning)
- **Multiple camera support** (concurrent front/back cameras)
- **Fully customizable UI** or built-in interface
- **Real-time camera controls** (zoom, flash, exposure)

### Features Matrix

| Feature | Android | iOS |
|---------|---------|-----|
| Ask permissions | ✅ | ✅ |
| Record video | ✅ | ✅ |
| Multi camera (BETA) | ✅ | ✅ |
| Enable/disable audio | ✅ | ✅ |
| Take photos | ✅ | ✅ |
| Photo live filters | ✅ | ✅ |
| Exposure level | ✅ | ✅ |
| Live image stream | ✅ | ✅ |
| Image analysis | ✅ | ✅ |
| Zoom | ✅ | ✅ |
| Device flash support | ✅ | ✅ |
| Auto focus | ✅ | ✅ |
| Live switching camera | ✅ | ✅ |
| Camera rotation stream | ✅ | ✅ |
| Background auto stop | ✅ | ✅ |
| Sensor type switching | ⛔️ | ✅ |
| Front camera mirroring | ✅ | ✅ |

## Installation

### 1. Add Dependency

Add to your `pubspec.yaml`:

```yaml
dependencies:
  camerawesome: ^2.4.0
```

Then run:

```bash
flutter pub get
```

### 2. Import the Package

```dart
import 'package:camerawesome/camerawesome_plugin.dart';
```

## Platform Setup

### iOS Configuration

Add these permissions to `ios/Runner/Info.plist`:

```xml
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to take photos and record videos</string>

<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access to record audio with videos</string>

<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs location access to add GPS data to photos</string>
```

### Android Configuration

1. **Update minimum SDK version** in `android/app/build.gradle`:

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21  // Required minimum
        targetSdkVersion 34
    }
}
```

2. **Add permissions** to `android/app/src/main/AndroidManifest.xml`:

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Camera permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- Audio recording (for video with sound) -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    
    <!-- Location (for EXIF data) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- Storage (if saving to external storage) -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
                     android:maxSdkVersion="28" />

    <application>
        <!-- Your app configuration -->
    </application>
</manifest>
```

## Basic Usage

### Simple Camera Implementation

```dart
import 'package:flutter/material.dart';
import 'package:camerawesome/camerawesome_plugin.dart';

class CameraPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photoAndVideo(
          initialCaptureMode: CaptureMode.photo,
        ),
        onMediaTap: (mediaCapture) {
          // Handle captured media
          print('Media captured: ${mediaCapture.filePath}');
        },
      ),
    );
  }
}
```

## Built-in UI

The awesome UI provides a complete camera experience out of the box:

### Basic Awesome UI

```dart
CameraAwesomeBuilder.awesome(
  saveConfig: SaveConfig.photoAndVideo(),
  onMediaTap: (mediaCapture) {
    // Open the captured file
    OpenFile.open(mediaCapture.filePath);
  },
)
```

### Customized Awesome UI

```dart
CameraAwesomeBuilder.awesome(
  // Save configuration
  saveConfig: SaveConfig.photoAndVideo(
    initialCaptureMode: CaptureMode.photo,
    photoPathBuilder: () {
      final directory = await getApplicationDocumentsDirectory();
      return '${directory.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    },
    videoPathBuilder: () {
      final directory = await getApplicationDocumentsDirectory();
      return '${directory.path}/${DateTime.now().millisecondsSinceEpoch}.mp4';
    },
  ),
  
  // Camera configuration
  sensorConfig: SensorConfig.single(
    sensor: Sensor.position(SensorPosition.back),
    flashMode: FlashMode.auto,
    aspectRatio: CameraAspectRatios.ratio_16_9,
    zoom: 0.0,
  ),
  
  // Initial settings
  enablePhysicalButton: true,
  filter: AwesomeFilter.None,
  
  // UI customization
  theme: AwesomeTheme(
    bottomActionsBackgroundColor: Colors.black.withOpacity(0.5),
    buttonTheme: AwesomeButtonTheme(
      backgroundColor: Colors.blue,
      iconSize: 32,
      padding: EdgeInsets.all(16),
    ),
  ),
  
  // Callbacks
  onMediaTap: (mediaCapture) {
    print('Media tapped: ${mediaCapture.filePath}');
  },
  onMediaCaptureEvent: (event) {
    print('Capture event: ${event.status}');
  },
)
```

## Custom UI

For complete control over the camera interface:

### Basic Custom Implementation

```dart
CameraAwesomeBuilder.custom(
  saveConfig: SaveConfig.photo(),
  builder: (cameraState, previewSize, previewRect) {
    return cameraState.when(
      onPreparingCamera: (state) => Center(
        child: CircularProgressIndicator(),
      ),
      onPhotoMode: (state) => PhotoModeUI(state: state),
      onVideoMode: (state) => VideoModeUI(state: state),
      onVideoRecordingMode: (state) => VideoRecordingUI(state: state),
    );
  },
)
```

### Custom Photo Mode UI

```dart
class PhotoModeUI extends StatelessWidget {
  final PhotoCameraState state;
  
  const PhotoModeUI({Key? key, required this.state}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Top controls
        Positioned(
          top: MediaQuery.of(context).padding.top + 16,
          left: 16,
          right: 16,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Flash toggle
              IconButton(
                onPressed: () => _toggleFlash(),
                icon: StreamBuilder<FlashMode>(
                  stream: state.sensorConfig.flashMode,
                  builder: (context, snapshot) {
                    final flashMode = snapshot.data ?? FlashMode.none;
                    return Icon(
                      _getFlashIcon(flashMode),
                      color: Colors.white,
                    );
                  },
                ),
              ),
              
              // Camera switch
              IconButton(
                onPressed: () => _switchCamera(),
                icon: Icon(Icons.flip_camera_ios, color: Colors.white),
              ),
            ],
          ),
        ),
        
        // Bottom controls
        Positioned(
          bottom: MediaQuery.of(context).padding.bottom + 32,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Gallery button
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: IconButton(
                  onPressed: () => _openGallery(),
                  icon: Icon(Icons.photo_library, color: Colors.white),
                ),
              ),
              
              // Capture button
              GestureDetector(
                onTap: () => _takePhoto(),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 4),
                  ),
                ),
              ),
              
              // Video mode button
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: IconButton(
                  onPressed: () => _switchToVideoMode(),
                  icon: Icon(Icons.videocam, color: Colors.white),
                ),
              ),
            ],
          ),
        ),
        
        // Zoom slider
        Positioned(
          right: 16,
          top: MediaQuery.of(context).size.height * 0.3,
          bottom: MediaQuery.of(context).size.height * 0.3,
          child: RotatedBox(
            quarterTurns: 3,
            child: StreamBuilder<double>(
              stream: state.sensorConfig.zoom,
              builder: (context, snapshot) {
                final zoom = snapshot.data ?? 0.0;
                return Slider(
                  value: zoom,
                  onChanged: (value) => state.sensorConfig.setZoom(value),
                  activeColor: Colors.white,
                  inactiveColor: Colors.white.withOpacity(0.3),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
  
  void _takePhoto() {
    state.takePhoto();
  }
  
  void _toggleFlash() {
    state.sensorConfig.flashMode.value.when(
      none: () => state.sensorConfig.setFlashMode(FlashMode.on),
      on: () => state.sensorConfig.setFlashMode(FlashMode.auto),
      auto: () => state.sensorConfig.setFlashMode(FlashMode.none),
      always: () => state.sensorConfig.setFlashMode(FlashMode.none),
    );
  }
  
  void _switchCamera() {
    state.switchCameraSensor();
  }
  
  void _switchToVideoMode() {
    state.switchToVideoMode();
  }
  
  void _openGallery() {
    // Implement gallery opening logic
  }
  
  IconData _getFlashIcon(FlashMode flashMode) {
    switch (flashMode) {
      case FlashMode.none:
        return Icons.flash_off;
      case FlashMode.on:
        return Icons.flash_on;
      case FlashMode.auto:
        return Icons.flash_auto;
      case FlashMode.always:
        return Icons.flash_on;
    }
  }
}
```

## Camera States

CamerAwesome uses a state-based approach for camera management:

### State Types

1. **PreparingCameraState**: Initial loading state
2. **PhotoCameraState**: Ready for photo capture
3. **VideoCameraState**: Ready for video recording
4. **VideoRecordingCameraState**: Currently recording video

### Working with States

```dart
builder: (cameraState, previewSize, previewRect) {
  return cameraState.when(
    onPreparingCamera: (state) {
      return Center(child: CircularProgressIndicator());
    },
    onPhotoMode: (state) {
      return Column(
        children: [
          Text('Photo Mode'),
          ElevatedButton(
            onPressed: () => state.takePhoto(),
            child: Text('Take Photo'),
          ),
          ElevatedButton(
            onPressed: () => state.switchToVideoMode(),
            child: Text('Switch to Video'),
          ),
        ],
      );
    },
    onVideoMode: (state) {
      return Column(
        children: [
          Text('Video Mode'),
          ElevatedButton(
            onPressed: () => state.startRecording(),
            child: Text('Start Recording'),
          ),
          ElevatedButton(
            onPressed: () => state.switchToPhotoMode(),
            child: Text('Switch to Photo'),
          ),
        ],
      );
    },
    onVideoRecordingMode: (state) {
      return Column(
        children: [
          Text('Recording...'),
          ElevatedButton(
            onPressed: () => state.stopRecording(),
            child: Text('Stop Recording'),
          ),
          ElevatedButton(
            onPressed: () => state.pauseRecording(),
            child: Text('Pause'),
          ),
        ],
      );
    },
  );
}
```

## Media Capture Events

Monitor capture events in real-time:

```dart
onMediaCaptureEvent: (event) {
  switch ((event.status, event.isPicture, event.isVideo)) {
    case (MediaCaptureStatus.capturing, true, false):
      print('Capturing picture...');
      // Show capturing indicator
      
    case (MediaCaptureStatus.success, true, false):
      print('Picture captured successfully');
      event.captureRequest.when(
        single: (single) {
          print('Picture saved: ${single.file?.path}');
          // Handle single photo
        },
        multiple: (multiple) {
          multiple.fileBySensor.forEach((sensor, file) {
            print('Picture from $sensor: ${file?.path}');
          });
        },
      );
      
    case (MediaCaptureStatus.failure, true, false):
      print('Failed to capture picture: ${event.exception}');
      // Show error message
      
    case (MediaCaptureStatus.capturing, false, true):
      print('Starting video recording...');
      
    case (MediaCaptureStatus.success, false, true):
      print('Video recording completed');
      event.captureRequest.when(
        single: (single) {
          print('Video saved: ${single.file?.path}');
        },
        multiple: (multiple) {
          multiple.fileBySensor.forEach((sensor, file) {
            print('Video from $sensor: ${file?.path}');
          });
        },
      );
      
    case (MediaCaptureStatus.failure, false, true):
      print('Failed to record video: ${event.exception}');
      
    default:
      print('Unknown event: $event');
  }
},
```

## Image Analysis

Use image analysis for ML features like face detection and barcode scanning:

### Basic Setup

```dart
CameraAwesomeBuilder.awesome(
  saveConfig: SaveConfig.photo(),
  onImageForAnalysis: (AnalysisImage image) {
    // Process the image for analysis
    _analyzeImage(image);
  },
  imageAnalysisConfig: AnalysisConfig(
    androidOptions: const AndroidAnalysisOptions.nv21(
      width: 250, // Target width for analysis
    ),
    autoStart: true,
    maxFramesPerSecond: 20, // Limit FPS for performance
  ),
)
```

### Face Detection Example

```dart
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

class FaceDetectionService {
  late final FaceDetector _faceDetector;
  
  FaceDetectionService() {
    _faceDetector = FaceDetector(
      options: FaceDetectorOptions(
        enableContours: true,
        enableLandmarks: true,
        enableClassification: true,
        enableTracking: true,
      ),
    );
  }
  
  Future<List<Face>> detectFaces(AnalysisImage analysisImage) async {
    final inputImage = _convertToInputImage(analysisImage);
    return await _faceDetector.processImage(inputImage);
  }
  
  InputImage _convertToInputImage(AnalysisImage analysisImage) {
    return InputImage.fromBytes(
      bytes: analysisImage.bytes,
      metadata: InputImageMetadata(
        size: Size(
          analysisImage.width.toDouble(),
          analysisImage.height.toDouble(),
        ),
        rotation: analysisImage.rotation,
        format: analysisImage.format,
        bytesPerRow: analysisImage.bytesPerRow,
      ),
    );
  }
  
  void dispose() {
    _faceDetector.close();
  }
}

// Usage in camera
CameraAwesomeBuilder.custom(
  saveConfig: SaveConfig.photo(),
  onImageForAnalysis: (image) async {
    final faces = await faceDetectionService.detectFaces(image);
    // Update UI with detected faces
    setState(() {
      _detectedFaces = faces;
    });
  },
  builder: (state, previewSize, previewRect) {
    return Stack(
      children: [
        // Camera preview is automatically rendered
        
        // Face overlay
        if (_detectedFaces.isNotEmpty)
          CustomPaint(
            painter: FaceOverlayPainter(_detectedFaces, previewSize),
            size: Size.infinite,
          ),
          
        // UI controls
        _buildCameraControls(state),
      ],
    );
  },
)
```

### Barcode Scanning Example

```dart
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';

class BarcodeScanner {
  late final BarcodeScanner _barcodeScanner;
  
  BarcodeScanner() {
    _barcodeScanner = BarcodeScanner(
      formats: [BarcodeFormat.all], // Scan all formats
    );
  }
  
  Future<List<Barcode>> scanBarcodes(AnalysisImage analysisImage) async {
    final inputImage = _convertToInputImage(analysisImage);
    return await _barcodeScanner.processImage(inputImage);
  }
  
  void dispose() {
    _barcodeScanner.close();
  }
}
```

## Photo Filters

Apply real-time filters to photos:

### Available Filters

```dart
// Set initial filter
CameraAwesomeBuilder.awesome(
  filter: AwesomeFilter.AddictiveRed,
  availableFilters: [
    AwesomeFilter.None,
    AwesomeFilter.AddictiveRed,
    AwesomeFilter.Aden,
    AwesomeFilter.Amaro,
    AwesomeFilter.Ashby,
    AwesomeFilter.Brannan,
    AwesomeFilter.Brooklyn,
    AwesomeFilter.Charmes,
    AwesomeFilter.Clarendon,
    AwesomeFilter.Crema,
    AwesomeFilter.Dogpatch,
    AwesomeFilter.Earlybird,
    AwesomeFilter.F1977,
    AwesomeFilter.Gingham,
    AwesomeFilter.Ginza,
    AwesomeFilter.Hefe,
    AwesomeFilter.Helena,
    AwesomeFilter.Hudson,
    AwesomeFilter.Inkwell,
    AwesomeFilter.Juno,
    AwesomeFilter.Kelvin,
    AwesomeFilter.Lark,
    AwesomeFilter.Lo_Fi,
    AwesomeFilter.Ludwig,
    AwesomeFilter.Maven,
    AwesomeFilter.Mayfair,
    AwesomeFilter.Moon,
    AwesomeFilter.Nashville,
    AwesomeFilter.Perpetua,
    AwesomeFilter.Reyes,
    AwesomeFilter.Rise,
    AwesomeFilter.Sierra,
    AwesomeFilter.Skyline,
    AwesomeFilter.Slumber,
    AwesomeFilter.Stinson,
    AwesomeFilter.Sutro,
    AwesomeFilter.Toaster,
    AwesomeFilter.Valencia,
    AwesomeFilter.Vesper,
    AwesomeFilter.Walden,
    AwesomeFilter.Willow,
    AwesomeFilter.X_Pro_II,
  ],
)
```

### Programmatically Change Filters

```dart
// In custom UI
cameraState.when(
  onPhotoMode: (state) {
    return Column(
      children: [
        // Filter selection
        Wrap(
          children: AwesomeFilter.values.map((filter) {
            return GestureDetector(
              onTap: () => state.setFilter(filter),
              child: Container(
                margin: EdgeInsets.all(4),
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  filter.name,
                  style: TextStyle(color: Colors.white),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  },
  // ... other states
);
```

## Sensor Configuration

Control camera settings dynamically:

### Basic Sensor Configuration

```dart
sensorConfig: SensorConfig.single(
  sensor: Sensor.position(SensorPosition.back),
  flashMode: FlashMode.auto,
  aspectRatio: CameraAspectRatios.ratio_16_9,
  zoom: 0.0,
)
```

### Dynamic Control

```dart
// In your custom UI
StreamBuilder<double>(
  stream: state.sensorConfig.zoom,
  builder: (context, snapshot) {
    final currentZoom = snapshot.data ?? 0.0;
    return Slider(
      value: currentZoom,
      min: 0.0,
      max: 1.0,
      onChanged: (value) {
        state.sensorConfig.setZoom(value);
      },
    );
  },
)

// Flash mode control
StreamBuilder<FlashMode>(
  stream: state.sensorConfig.flashMode,
  builder: (context, snapshot) {
    final flashMode = snapshot.data ?? FlashMode.none;
    return IconButton(
      icon: Icon(_getFlashIcon(flashMode)),
      onPressed: () {
        final nextMode = _getNextFlashMode(flashMode);
        state.sensorConfig.setFlashMode(nextMode);
      },
    );
  },
)

// Brightness control
StreamBuilder<double>(
  stream: state.sensorConfig.brightness,
  builder: (context, snapshot) {
    final brightness = snapshot.data ?? 0.0;
    return Slider(
      value: brightness,
      min: -1.0,
      max: 1.0,
      onChanged: (value) {
        state.sensorConfig.setBrightness(value);
      },
    );
  },
)

// Mirror front camera
StreamBuilder<bool>(
  stream: state.sensorConfig.mirrorFrontCamera,
  builder: (context, snapshot) {
    final isMirrored = snapshot.data ?? false;
    return Switch(
      value: isMirrored,
      onChanged: (value) {
        state.sensorConfig.setMirrorFrontCamera(value);
      },
    );
  },
)
```

## Multiple Cameras

**⚠️ Beta Feature** - Use concurrent cameras for advanced use cases:

### Setup Multiple Cameras

```dart
CameraAwesomeBuilder.awesome(
  sensorConfig: SensorConfig.multiple(
    sensors: [
      Sensor.position(SensorPosition.back),
      Sensor.position(SensorPosition.front),
    ],
    flashMode: FlashMode.auto,
    aspectRatio: CameraAspectRatios.ratio_16_9,
  ),
  saveConfig: SaveConfig.photoAndVideo(),
  onMediaCaptureEvent: (event) {
    if (event.status == MediaCaptureStatus.success) {
      event.captureRequest.when(
        single: (single) {
          // Single camera capture
          print('Single capture: ${single.file?.path}');
        },
        multiple: (multiple) {
          // Multiple camera capture
          multiple.fileBySensor.forEach((sensor, file) {
            print('Sensor $sensor: ${file?.path}');
          });
        },
      );
    }
  },
)
```

### Limitations

- Not supported by all devices
- Performance impact with multiple streams
- Limited resolution options
- Check device capability: `CameraCharacteristics.isVideoRecordingAndImageAnalysisSupported()`

## Best Practices

### 1. Performance Optimization

```dart
// Limit analysis frame rate
imageAnalysisConfig: AnalysisConfig(
  maxFramesPerSecond: 10, // Don't overprocess
  androidOptions: const AndroidAnalysisOptions.nv21(
    width: 320, // Use smaller resolution for analysis
  ),
)

// Dispose resources properly
@override
void dispose() {
  faceDetectionService.dispose();
  barcodeScanner.dispose();
  super.dispose();
}
```

### 2. Error Handling

```dart
onMediaCaptureEvent: (event) {
  if (event.status == MediaCaptureStatus.failure) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Capture failed: ${event.exception}'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
```

### 3. Permissions Handling

```dart
class CameraPermissionHelper {
  static Future<bool> checkPermissions() async {
    final cameraStatus = await Permission.camera.status;
    final microphoneStatus = await Permission.microphone.status;
    
    return cameraStatus.isGranted && microphoneStatus.isGranted;
  }
  
  static Future<bool> requestPermissions() async {
    final Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
      Permission.microphone,
      Permission.storage,
    ].request();
    
    return statuses[Permission.camera]?.isGranted == true &&
           statuses[Permission.microphone]?.isGranted == true;
  }
}

// Use before showing camera
class CameraScreen extends StatefulWidget {
  @override
  _CameraScreenState createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  bool _hasPermissions = false;
  
  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }
  
  Future<void> _checkPermissions() async {
    final hasPermissions = await CameraPermissionHelper.checkPermissions();
    
    if (!hasPermissions) {
      final granted = await CameraPermissionHelper.requestPermissions();
      setState(() {
        _hasPermissions = granted;
      });
    } else {
      setState(() {
        _hasPermissions = true;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (!_hasPermissions) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.camera_alt, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('Camera permissions required'),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: _checkPermissions,
                child: Text('Grant Permissions'),
              ),
            ],
          ),
        ),
      );
    }
    
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        // Your camera configuration
      ),
    );
  }
}
```

### 4. File Management

```dart
class MediaFileManager {
  static Future<String> getPhotoPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/photos/${DateTime.now().millisecondsSinceEpoch}.jpg';
  }
  
  static Future<String> getVideoPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/videos/${DateTime.now().millisecondsSinceEpoch}.mp4';
  }
  
  static Future<void> createDirectories() async {
    final directory = await getApplicationDocumentsDirectory();
    final photoDir = Directory('${directory.path}/photos');
    final videoDir = Directory('${directory.path}/videos');
    
    if (!await photoDir.exists()) {
      await photoDir.create(recursive: true);
    }
    
    if (!await videoDir.exists()) {
      await videoDir.create(recursive: true);
    }
  }
}

// Use in save config
saveConfig: SaveConfig.photoAndVideo(
  photoPathBuilder: MediaFileManager.getPhotoPath,
  videoPathBuilder: MediaFileManager.getVideoPath,
)
```

## Troubleshooting

### Common Issues

#### 1. Camera Initialization Failed

```dart
// Check permissions first
final hasPermissions = await Permission.camera.isGranted;
if (!hasPermissions) {
  await Permission.camera.request();
}

// Check camera availability
final cameras = await availableCameras();
if (cameras.isEmpty) {
  throw Exception('No cameras available');
}
```

#### 2. Recording Fails on Android

```dart
// Ensure proper permissions in AndroidManifest.xml
// Check if device supports video recording with image analysis
final supportsSimultaneous = await CameraCharacteristics
    .isVideoRecordingAndImageAnalysisSupported(Sensors.back);

if (!supportsSimultaneous) {
  // Disable image analysis during video recording
  imageAnalysisConfig: AnalysisConfig(autoStart: false),
}
```

#### 3. Image Analysis Performance Issues

```dart
// Reduce analysis frequency
imageAnalysisConfig: AnalysisConfig(
  maxFramesPerSecond: 5, // Lower FPS
  androidOptions: const AndroidAnalysisOptions.nv21(
    width: 240, // Smaller resolution
  ),
)

// Process analysis asynchronously
onImageForAnalysis: (image) {
  // Don't await - process in background
  _processImageAsync(image);
}

Future<void> _processImageAsync(AnalysisImage image) async {
  // Use compute for heavy processing
  final result = await compute(_analyzeImage, image);
  // Update UI on main thread
  if (mounted) {
    setState(() {
      _analysisResult = result;
    });
  }
}
```

#### 4. Memory Issues

```dart
// Dispose resources properly
@override
void dispose() {
  _faceDetector?.close();
  _barcodeScanner?.close();
  _cameraController?.dispose();
  super.dispose();
}

// Limit concurrent operations
class AnalysisManager {
  bool _isProcessing = false;
  
  Future<void> processImage(AnalysisImage image) async {
    if (_isProcessing) return; // Skip if already processing
    
    _isProcessing = true;
    try {
      // Process image
      await _doAnalysis(image);
    } finally {
      _isProcessing = false;
    }
  }
}
```

### Debug Tips

```dart
// Enable debug logging
saveConfig: SaveConfig.photoAndVideo(
  exifPreferences: ExifPreferences(saveGPSLocation: true),
),

// Monitor camera state changes
builder: (cameraState, previewSize, previewRect) {
  print('Camera state: ${cameraState.runtimeType}');
  print('Preview size: $previewSize');
  print('Preview rect: $previewRect');
  
  return YourCameraUI();
}

// Log capture events
onMediaCaptureEvent: (event) {
  print('Capture event: ${event.status} - ${event.isPicture ? 'Photo' : 'Video'}');
  if (event.exception != null) {
    print('Error: ${event.exception}');
  }
}
```

## Conclusion

CamerAwesome provides a comprehensive camera solution for Flutter apps. Whether you need a simple camera interface or advanced features like multi-camera support and real-time image analysis, this plugin offers the flexibility and performance needed for modern camera applications.

For more examples and advanced usage, check the [official GitHub repository](https://github.com/Apparence-io/camera_awesome) and [documentation](https://apparencekit.dev/docs/camera_awesome).
