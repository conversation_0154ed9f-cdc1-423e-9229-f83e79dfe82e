# CI/CD Setup for Bloomg Flutter

## Overview

This document provides guidance on setting up Continuous Integration and Continuous Deployment (CI/CD) for the Bloomg Flutter project using Bitbucket Pipelines.

## Current Issue

The project currently has a `.github/workflows/main.yaml` file which is **incorrect** for this Bitbucket repository. GitHub Actions workflows do not work with Bitbucket repositories.

## Bitbucket Pipelines Configuration

### Setup Instructions

1. **Remove GitHub Actions Workflow**
   ```bash
   rm .github/workflows/main.yaml
   ```

2. **Use Bitbucket Pipelines**
   - The `bitbucket-pipelines.yml` file has been created in the repository root
   - This is the correct CI/CD configuration for Bitbucket repositories

3. **Enable Pipelines in Bitbucket**
   - Go to your Bitbucket repository
   - Navigate to Repository settings > Pipelines > Settings
   - Enable Pipelines for your repository

### Pipeline Configuration Details

The `bitbucket-pipelines.yml` file includes:

#### **Default Pipeline**
- Runs on every push to any branch
- Executes Flutter tests with coverage

#### **Branch-Specific Pipelines**

**Main Branch:**
- Runs tests
- Builds production Android APK
- Builds production iOS app (without code signing)

**Develop Branch:**
- Runs tests
- Builds development APK with development flavor

**Staging Branch:**
- Runs tests
- Builds staging APK with staging flavor

#### **Pull Request Pipeline**
- Runs tests for all pull requests
- Ensures code quality before merging

#### **Tag Pipeline**
- Triggers on version tags (v*)
- Runs full test and build suite
- Includes release creation step

### Flutter Flavors Support

The pipeline supports all three Flutter flavors:

```bash
# Development
flutter build apk --flavor development --target lib/main_development.dart

# Staging
flutter build apk --flavor staging --target lib/main_staging.dart

# Production
flutter build apk --flavor production --target lib/main_production.dart
```

### Caching

The configuration includes Flutter pub cache to speed up builds:
- Cache key: `flutter`
- Cache path: `~/.pub-cache`

### Artifacts

Build artifacts are preserved:
- Android APKs: `build/app/outputs/flutter-apk/*.apk`
- iOS apps: `build/ios/iphoneos/*.app`
- Test coverage: `coverage/**`

## Next Steps

### 1. Repository Cleanup
```bash
# Remove incorrect GitHub Actions workflow
git rm .github/workflows/main.yaml
git commit -m "Remove GitHub Actions workflow (incorrect for Bitbucket)"
```

### 2. Environment Configuration

Update the main entry point files to include environment-specific configurations:

**lib/main_development.dart:**
```dart
import 'package:bloomg_flutter/app/app.dart';
import 'package:bloomg_flutter/bootstrap.dart';

void main() {
  bootstrap(() => const App(environment: Environment.development));
}
```

### 3. Code Signing (iOS)

For iOS builds, you'll need to:
1. Add provisioning profiles to Bitbucket repository variables
2. Configure code signing in the pipeline
3. Update the iOS build step to include signing

### 4. Deployment

Add deployment steps for:
- App Store Connect (iOS)
- Google Play Console (Android)
- Firebase App Distribution (for testing)

## Pipeline Variables

Configure these in Bitbucket Repository Settings > Pipelines > Repository variables:

- `ANDROID_KEYSTORE`: Base64 encoded Android keystore
- `ANDROID_KEY_ALIAS`: Android key alias
- `ANDROID_STORE_PASSWORD`: Android store password
- `ANDROID_KEY_PASSWORD`: Android key password
- `IOS_CERTIFICATE`: Base64 encoded iOS certificate
- `IOS_PROVISIONING_PROFILE`: Base64 encoded provisioning profile

## Testing the Pipeline

1. Push the `bitbucket-pipelines.yml` file to your repository
2. Create a pull request to test the PR pipeline
3. Merge to main to test the full build pipeline
4. Create a tag (e.g., `v1.0.0`) to test the release pipeline

## Troubleshooting

### Common Issues

1. **Flutter version mismatch**: Ensure the Docker image version matches your local Flutter version
2. **Dependency conflicts**: Clear pub cache if builds fail
3. **Flavor configuration**: Verify Android flavor configuration in `android/app/build.gradle`

### Useful Commands

```bash
# Test locally before pushing
flutter analyze
flutter test --coverage
flutter build apk --flavor development --target lib/main_development.dart

# Check pipeline status
# Go to Bitbucket repository > Pipelines
```

## Resources

- [Bitbucket Pipelines Documentation](https://support.atlassian.com/bitbucket-cloud/docs/get-started-with-bitbucket-pipelines/)
- [Flutter CI/CD Best Practices](https://docs.flutter.dev/deployment/cd)
- [Bitbucket Pipelines for Flutter](https://bitbucket.org/blog/flutter-bitbucket-pipelines)
