name: bloomg_flutter
description: bloomg_flutter by Algomash
version: 1.0.0+1
publish_to: none

environment:
  sdk: ^3.5.0

dependencies:
  bloc: ^8.1.4
  camerawesome: ^2.4.0
  cloud_firestore: ^5.6.8
  equatable: ^2.0.7
  firebase_auth: ^5.5.4
  firebase_core: ^3.6.0
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_localizations:
    sdk: flutter
  flutter_svg: ^2.0.10+1
  formz: ^0.8.0
  get_it: ^8.0.3
  go_router: ^15.1.3
  google_mlkit_face_detection: ^0.13.1
  google_sign_in: ^6.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.2.2
  intl: ^0.19.0
  json_annotation: ^4.9.0
  logger: ^2.5.0
  path_provider: ^2.1.5
  responsive_framework: ^1.5.1
  sign_in_with_apple: ^7.0.1
  video_player: ^2.9.2
  video_thumbnail: ^0.5.3

dev_dependencies:
  bloc_test: ^9.1.7
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  hive_generator: ^2.0.1
  mocktail: ^1.0.4
  very_good_analysis: ^6.0.0

flutter:
  uses-material-design: true
  generate: true

# Flutter Gen Configuration
flutter_gen:
  output: lib/gen/
  line_length: 80
  synthetic-package: false
  integrations:
    flutter_svg: true
  assets:
    enabled: true
    package_parameter_enabled: false
    style: snake-case
  fonts:
    enabled: true
  colors:
    enabled: false
